{"format": 1, "restore": {"C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj": {}}, "projects": {"C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj", "projectName": "MyFirstScript", "projectPath": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netframework48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "netframework48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "netframework48", "dependencies": {"Mal.Mdk2.PbAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.1.13, )"}, "Mal.Mdk2.PbPackager": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.1.5, )"}, "Mal.Mdk2.References": {"target": "Package", "version": "[2.2.4, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}}}}