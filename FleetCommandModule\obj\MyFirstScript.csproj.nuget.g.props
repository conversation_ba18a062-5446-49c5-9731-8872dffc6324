﻿<?xml version="1.0" encoding="utf-8" standalone="no"?>
<Project ToolsVersion="14.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <RestoreSuccess Condition=" '$(RestoreSuccess)' == '' ">True</RestoreSuccess>
    <RestoreTool Condition=" '$(RestoreTool)' == '' ">NuGet</RestoreTool>
    <ProjectAssetsFile Condition=" '$(ProjectAssetsFile)' == '' ">$(MSBuildThisFileDirectory)project.assets.json</ProjectAssetsFile>
    <NuGetPackageRoot Condition=" '$(NuGetPackageRoot)' == '' ">$(UserProfile)\.nuget\packages\</NuGetPackageRoot>
    <NuGetPackageFolders Condition=" '$(NuGetPackageFolders)' == '' ">C:\Users\<USER>\.nuget\packages\;C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages</NuGetPackageFolders>
    <NuGetProjectStyle Condition=" '$(NuGetProjectStyle)' == '' ">PackageReference</NuGetProjectStyle>
    <NuGetToolVersion Condition=" '$(NuGetToolVersion)' == '' ">6.14.0</NuGetToolVersion>
  </PropertyGroup>
  <ItemGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <SourceRoot Include="C:\Users\<USER>\.nuget\packages\" />
    <SourceRoot Include="C:\Program Files (x86)\Microsoft Visual Studio\Shared\NuGetPackages\" />
  </ItemGroup>
  <ImportGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <Import Project="$(NuGetPackageRoot)mal.mdk2.pbpackager\2.1.5\build\Mal.Mdk2.PbPackager.props" Condition="Exists('$(NuGetPackageRoot)mal.mdk2.pbpackager\2.1.5\build\Mal.Mdk2.PbPackager.props')" />
    <Import Project="$(NuGetPackageRoot)mal.mdk2.pbanalyzers\2.1.13\build\Mal.MDK2.PbAnalyzers.props" Condition="Exists('$(NuGetPackageRoot)mal.mdk2.pbanalyzers\2.1.13\build\Mal.MDK2.PbAnalyzers.props')" />
  </ImportGroup>
  <PropertyGroup Condition=" '$(ExcludeRestorePackageImports)' != 'true' ">
    <PkgMal_Mdk2_References Condition=" '$(PkgMal_Mdk2_References)' == '' ">C:\Users\<USER>\.nuget\packages\mal.mdk2.references\2.2.4</PkgMal_Mdk2_References>
    <PkgMal_Mdk2_PbPackager Condition=" '$(PkgMal_Mdk2_PbPackager)' == '' ">C:\Users\<USER>\.nuget\packages\mal.mdk2.pbpackager\2.1.5</PkgMal_Mdk2_PbPackager>
    <PkgMal_Mdk2_PbAnalyzers Condition=" '$(PkgMal_Mdk2_PbAnalyzers)' == '' ">C:\Users\<USER>\.nuget\packages\mal.mdk2.pbanalyzers\2.1.13</PkgMal_Mdk2_PbAnalyzers>
  </PropertyGroup>
</Project>