# Fleet Command 2x2 LCD Array Modification

## Overview
This is a modified version of Rdav's Fleet Command Beta script that has been updated to work with a 2x2 LCD array, providing a total display resolution of 200x200 pixels instead of the original 100x100.

## Files
- `FleetDisplayScript2_2x2LCD_Complete.cs` - The complete modified script for 2x2 LCD array
- `FleetDisplayScript2.cs` - Original script (100x100 single LCD)

## Key Changes Made

### 1. Display Resolution
- **Original**: 100x100 pixels on single LCD
- **Modified**: 200x200 pixels across 2x2 LCD array (4 panels of 100x100 each)

### 2. LCD Panel Setup
The script now requires 4 LCD panels with specific names:
- `RFC_PANEL_TL` - Top Left panel
- `RFC_PANEL_TR` - Top Right panel  
- `RFC_PANEL_BL` - Bottom Left panel
- `RFC_PANEL_BR` - Bottom Right panel

### 3. Technical Modifications

#### Display Variables
- `ROWS_CT` and `COLUMNS_CT` increased from 100 to 200
- Added separate allocation arrays for each panel: `ALLOC_TL`, `ALLOC_TR`, `ALLOC_BL`, `ALLOC_BR`
- Updated UI positioning constants (doubled most values)

#### Text Rendering
- Modified text writer to split 200x200 display into 4 quadrants
- Each quadrant is rendered to its corresponding LCD panel
- Maintains synchronization across all 4 panels

#### UI Scaling
- Mouse interaction areas doubled in size
- Health bars and ship counters repositioned for larger display
- Proximity detection ranges increased for better usability

#### Panel Initialization
- `UIINIT()` function updated to find and initialize all 4 LCD panels
- Added error checking to verify all panels are found
- Background initialization handles larger 200x200 grid

### 4. User Interface Improvements
- Larger display area provides better visibility of fleet operations
- More space for ship symbols and status information
- Enhanced readability of text and numbers
- Improved mouse interaction precision

## Installation Instructions

### 1. LCD Panel Setup
1. Place 4 LCD panels in a 2x2 configuration
2. Rename them exactly as follows:
   - Top Left: `RFC_PANEL_TL`
   - Top Right: `RFC_PANEL_TR`
   - Bottom Left: `RFC_PANEL_BL`
   - Bottom Right: `RFC_PANEL_BR`

### 2. Script Installation
1. Copy the contents of `FleetDisplayScript2_2x2LCD_Complete.cs`
2. Paste into a Programmable Block named `CENTRAL_COMMAND`
3. Ensure you have a Remote Control block named `RFC_RC`
4. Run the script

### 3. Verification
The script will output error messages if any LCD panels are not found:
- `ERROR: RFC_PANEL_TL not found!`
- `ERROR: RFC_PANEL_TR not found!`
- `ERROR: RFC_PANEL_BL not found!`
- `ERROR: RFC_PANEL_BR not found!`

Make sure all panels are properly named and accessible.

## Usage
The script operates identically to the original, but with enhanced display capabilities:
- Use the Remote Control block to navigate the interface
- Mouse interactions work the same but with improved precision
- All fleet command functions remain unchanged
- Enjoy the larger, more detailed display

## Compatibility
- Requires Space Engineers with IGC (Inter-Grid Communication) support
- Compatible with existing fleet drone scripts
- Maintains all original functionality while adding enhanced display

## Performance Notes
- Slightly higher performance requirements due to 4x display area
- Text rendering split across 4 panels may have minor performance impact
- Overall performance should remain acceptable for most systems

## Troubleshooting

### Common Issues
1. **Panels not displaying**: Check panel names are exactly correct
2. **Misaligned display**: Ensure panels are in correct 2x2 physical arrangement
3. **Performance issues**: Consider reducing update frequency if needed

### Panel Arrangement
```
[RFC_PANEL_TL] [RFC_PANEL_TR]
[RFC_PANEL_BL] [RFC_PANEL_BR]
```

The panels should be physically arranged as shown above for proper display alignment.

## Credits
- Original Fleet Command script by Rdav
- 2x2 LCD modification by AI Assistant
- Updated for current Space Engineers API - 2024
