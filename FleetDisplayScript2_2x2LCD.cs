 #region Introduction
            /*
            Introduction
            ----------------
            Hello and thank you for downloading Rdav's Fleet Command Beta (Updated for Current SE)
            Rdav's fleet command is a total-conversion code for automatic fleets.
            This code allows for artificially intelligent drone operation, drones
            adopt unique and intelligent behaviour allowing them to engage targets
            follow commands, dock, undock along with giving players advanced
            control capabilities for an entire armada.
            Please see the workshop page for more details;

            You are currently looking at the 'Central Command' Unit, which
            is the main hub of operations for the code, only ONE of these units
            should be used per-fleet, others can be kept as backups, but only one
            should be operational at any one time.

            Rdav 28/08/17
            Updated for current Space Engineers API - 2024
            Modified for 2x2 LCD Array (200x200 total display) - 2024

            Installation
            --------------
            The code should come in a Pre-Fab format to make installation a breeze.
            Setup of these modules manually can be achieved however for streamlining
            or otherwise making the components of the module smaller, please refer to
            the manual to do so.

            The Central Command Unit will automatically:
             * Use IGC (Inter-Grid Communication) for fleet communication
             * Find a command seat used for issuing commands called 'RFC_RC' (only renaming required)
             * Find and use for target detection any turret with appropriate naming
             * Use 2x2 LCD array for 200x200 total display resolution

            LCD Panel Naming Convention:
            - RFC_PANEL_TL (Top Left)
            - RFC_PANEL_TR (Top Right)
            - RFC_PANEL_BL (Bottom Left)
            - RFC_PANEL_BR (Bottom Right)

            Bughunting/Problems
            --------------------
            The code will automatically create a bugreport and save it to the custom-data of the
            designated remote block.

            Suggestions Planned Features
            -----------------------------
            - Enhanced fleet coordination features
            - Improved UI responsiveness

             ChangeLog:
             * Updated to use IGC (Inter-Grid Communication) system
             * Removed deprecated antenna communication methods
             * Updated for current Space Engineers API compatibility
             * 18/01/2017 ln255 Added support for alternate orientations.
             * 18/01/2017 DETECT changed convergance to 100-800m default
             * added command recieved indicator to LCD
             * updated command recieved indicator
             * Fixed UI unresponsiveness
             * Updated to reflect self ship docking
             * Updated to draw a line for a go-to
             * Modified for 2x2 LCD array support (200x200 total display)
            */

          

            #endregion

            #region Syst Constants

            //Display Setup
            //---------------------------------------------------------
            string VERSION = "Ver_008_Updated_2x2LCD";                   //Script Current Version

            //IGC Communication Setup
            //---------------------------------------------------------
            const string IGCTagOUT = "RFC_FLEET_OUT";
            const string IGCTagIN = "RFC_FLEET_IN";
            IMyBroadcastListener IGCListener;
            const char P = medBlue; //Primary System Colour (your own ships)
            const char B = ' '; //Background Colour
            const char L1 = black; //Layer1background1colour
            const char L2 = mediumGray; //Layer2background1colour
            public char[,] BDRAW = new char[ROWS_CT, COLUMNS_CT]; //Stores Background
            char[,] DRAW = new char[ROWS_CT, COLUMNS_CT]; //Temporary Assigner

            #region Lettercodes
            const char red = '\uE200';
            const char medRed = '\uE1C0';
            const char darkRed = '\uE180';

            const char green = '\uE120';
            const char medGreen = '\uE118';
            const char darkGreen = '\uE110';

            const char blue = '\uE104';
            const char medBlue = '\uE103';
            const char darkBlue = '\uE102';

            const char yellow = '\uE220';
            const char medYellow = '\uE1D8';
            const char darkYellow = '\uE190';

            const char magenta = '\uE204';
            const char medMagenta = '\uE1C3';
            const char darkMagenta = '\uE182';

            const char cyan = '\uE124';
            const char medCyan = '\uE11B';
            const char darkCyan = '\uE112';

            const char white = '\uE2FF';
            const char lightGray = '\uE1DB';
            const char mediumGray = '\uE192';
            const char darkGray = '\uE149';
            const char black = '\uE100';
            #endregion

            //System Data Management
            //------------------------
            class DC_INF_INFO
            {
                public int SIZE; //Size of target
                public string TYPE; //Type of target
                public Vector3D POSITION; //Position
                public string DIRECT_TO_OUTPUT; //Directly Outputted string
                public int ST_SIZE; //Start Size
                public Vector2D UIPOS; // Position of UI locator
            }
            class DRONE_INFO
            {
                public string ID; //Drone Identifier (contains classification)
                public string COMLOC; //Drone Command &amp; locator
                public string GLOC; //Generated Locator
                public Vector3D LOC; //Current Drone Location
                public Vector3D VEL; //Drone Velocity
                public Vector3D TVEl; //Target Velocity vector
                public string ISDOCKED; //id of docked ship (own Id if not docked)
                public double HEALTH; //Health of the ship
                public DateTime LAST_PING; //Last recieved ping from the ship
                public string EXT_INF; //Drone Extra Info
                public string OUTPUT;   // String Drone Data Output
                public Vector2D UIPOS; // Position of UI locator (UI Only)

                //Standardised System Of Updating And Saving Drone Data
                public static DRONE_INFO DRONE_DATA_RS(string IN_ARG, DRONE_INFO DRONE_INF, bool[] RUN_ID)
                {
                    //Retrieves Data From Store
                    string[] DRN_INFO = IN_ARG.Split('*');
                    DRONE_INF.ID = (RUN_ID[0] != true) ? DRONE_INF.ID : DRN_INFO[0];
                    DRONE_INF.COMLOC = (RUN_ID[1] != true) ? DRONE_INF.COMLOC : DRN_INFO[1];
                    DRONE_INF.GLOC = (RUN_ID[2] != true) ? DRONE_INF.GLOC : DRN_INFO[2];
                    if (RUN_ID[3] == true) { Vector3D.TryParse(DRN_INFO[3], out DRONE_INF.LOC); }
                    if (RUN_ID[4] == true) { Vector3D.TryParse(DRN_INFO[4], out DRONE_INF.VEL); }
                    if (RUN_ID[5] == true) { Vector3D.TryParse(DRN_INFO[5], out DRONE_INF.TVEl); }
                    if (RUN_ID[6] == true) { DRONE_INF.ISDOCKED = DRN_INFO[6]; }
                    if (RUN_ID[7] == true) { DRONE_INF.HEALTH = double.Parse(DRN_INFO[7]); }
                    if (RUN_ID[8] == true) { DRONE_INF.LAST_PING = DateTime.Parse(DRN_INFO[8]); }
                    if (RUN_ID[9] == true) { DRONE_INF.EXT_INF = DRN_INFO[9]; }
                    return DRONE_INF;
                }

                public static DRONE_INFO SAVE(DRONE_INFO DRONE_INF)
                {
                    DRONE_INF.OUTPUT = string.Join("*", "#" + DRONE_INF.ID, DRONE_INF.COMLOC, DRONE_INF.GLOC, DRONE_INF.LOC, DRONE_INF.VEL, DRONE_INF.TVEl, DRONE_INF.ISDOCKED, DRONE_INF.HEALTH, DRONE_INF.LAST_PING, DRONE_INF.EXT_INF, "#" + DRONE_INF.ID);
                    return DRONE_INF;
                }
            }
            class DOCKPOINT_INFO
            {
                public string ID; //Dockpoint Identifier (contains docktype classification)
                public Vector3D LOC; //Current Dockpoint Location
                public string BASE_TAG; //ID of base ship
                public string ISDOCKED; //Id of docked ship (own Id if not docked)
                public DateTime LAST_PING; //Last recieved ping from the dockpoint
                public string OUTPUTROLL; //Coordinates package for drones to interperate
                public string OUTPUT;   // String Drone Data Output

                public List<IMyTerminalBlock> ROUTE; //List of route (drone ship only, not updated)

                //Standardised System Of Updating And Saving Drone Data
                public static DOCKPOINT_INFO DOCK_DATA_RS(string IN_ARG, DOCKPOINT_INFO DOCKPT_INF, bool[] RUN_ID)
                {
                    //Retrieves Data From Store
                    string[] DCK_INFO = IN_ARG.Split('*');
                    if (RUN_ID[0] == true) { DOCKPT_INF.ID = DCK_INFO[0]; }
                    if (RUN_ID[1] == true) { Vector3D.TryParse(DCK_INFO[1], out DOCKPT_INF.LOC); }
                    if (RUN_ID[2] == true) { DOCKPT_INF.BASE_TAG = DCK_INFO[2]; }
                    if (RUN_ID[3] == true) { DOCKPT_INF.ISDOCKED = DCK_INFO[3]; }
                    if (RUN_ID[4] == true) { DOCKPT_INF.LAST_PING = DateTime.Parse(DCK_INFO[4]); }
                    if (RUN_ID[5] == true) { DOCKPT_INF.OUTPUTROLL = DCK_INFO[5]; }

                    DOCKPT_INF.OUTPUT = string.Join("*", "#" + DOCKPT_INF.ID, DOCKPT_INF.LOC, DOCKPT_INF.BASE_TAG, DOCKPT_INF.ISDOCKED, DOCKPT_INF.LAST_PING, DOCKPT_INF.OUTPUTROLL, "#" + DOCKPT_INF.ID);
                    return DOCKPT_INF;
                }

                //Standardised DockString Saving Procedure
                public static DOCKPOINT_INFO SAVE_ROUTE_TO_STRING(DOCKPOINT_INFO DOCKPT_INFO)
                {
                    List<string> OUTPUT = new List<string>();
                    double OFFSET_CONST = 2;
                    List<IMyTerminalBlock> DOCKPT_TRAIL = DOCKPT_INFO.ROUTE;

                    //Adds First Ordinates (self and forwards position)
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (1.5), 2) + "");
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (OFFSET_CONST + 2.5), 2) + "");

                    //Iterates Through List Of LCD's
                    for (int i = 1; i < DOCKPT_TRAIL.Count; i++)
                    { var IMYPLACE = DOCKPT_TRAIL[i]; OUTPUT.Add(Vector3D.Round(IMYPLACE.GetPosition() + IMYPLACE.WorldMatrix.Backward * OFFSET_CONST, 2) + ""); }

                    //Adds Final Position
                    OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].GetPosition() +
                        DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Backward * OFFSET_CONST + DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Up * 100, 2) + "");


                    //Saves To String, Updates Locator, (And Updates OUTPUT)
                    DOCKPT_INFO.OUTPUTROLL = string.Join("^", OUTPUT);
                    DOCKPT_INFO.LOC = Vector3D.Round(DOCKPT_TRAIL[0].GetPosition(), 2);
                    DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);

                    return DOCKPT_INFO;
                }

                public static DOCKPOINT_INFO SAVE(DOCKPOINT_INFO DOCKPT_INFO)
                {
                    DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);
                    return DOCKPT_INFO;
                }

            }
            Dictionary<string, DC_INF_INFO> DECENTIN_INFO = new Dictionary<string, DC_INF_INFO>();
            Dictionary<string, DOCKPOINT_INFO> DOCKPOINTS = new Dictionary<string, DOCKPOINT_INFO>();
            Dictionary<string, DRONE_INFO> DRONES = new Dictionary<string, DRONE_INFO>();

            //Symbology Setup
            //-------------------------------------------
            class FIVE_THREE_NUMBERS
            {
                static char N = blue;
                static char[,] NUM_0 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, B, N }, { N, B, N }, { N, N, N } };
                static char[,] NUM_1 = new char[5, 3] { { N, N, B }, { B, N, B }, { B, N, B }, { B, N, B }, { N, N, N } };
                static char[,] NUM_2 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { N, B, B }, { N, N, N } };
                static char[,] NUM_3 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { B, B, N }, { N, N, N } };
                static char[,] NUM_4 = new char[5, 3] { { N, B, B }, { N, B, B }, { N, B, N }, { N, N, N }, { B, B, N } };
                static char[,] NUM_5 = new char[5, 3] { { N, N, N }, { N, B, B }, { N, N, N }, { B, B, N }, { N, N, N } };
                static char[,] NUM_6 = new char[5, 3] { { N, N, N }, { N, B, B }, { N, N, N }, { N, B, N }, { N, N, N } };
                static char[,] NUM_7 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { B, N, B }, { B, N, B } };
                static char[,] NUM_8 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, N, N }, { N, B, N }, { N, N, N } };
                static char[,] NUM_9 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, N, N }, { B, B, N }, { N, N, N } };

                public char[][,] NUMBERS = new char[][,] { NUM_0, NUM_1, NUM_2, NUM_3, NUM_4, NUM_5, NUM_6, NUM_7, NUM_8, NUM_9 };  //list of Numbers for external accessor
            } // Standard Numbers
            FIVE_THREE_NUMBERS SYST_NBRS = new FIVE_THREE_NUMBERS();

            class CURSOR_SYMBOLOGY
            {
                const char L1 = black;
                const char L2 = darkYellow;
                const char L3 = darkMagenta;
                const char L4 = cyan;
                const char L5 = green;
                const char L6 = red;

                //public char[,] Attack = new char[5, 5] { 
                //{B,B,B,B,L1},
                //{L1,B,B,L1,B},
                //{B,L1,L1,B,B},
                //{B,L1,L1,B,B},
                //{L1,B,B,L1,B}};

                public char[,] Attack = new char[5, 5] {
            {L6,L1,L1,L1,B},
            {L1,L6,L1,B,B},
            {L1,L1,L6,B,B},
            {L1,B,B,L1,B},
            {B,B,B,B,L1,}};

                public char[,] GoTo = new char[5, 5] {
            {L1,B,L1,B,B},
            {B,L1,B,L1,B},
            {B,L1,B,L1,B},
            {L1,B,L1,B,B},
            {B,B,B,B,B}};

                public char[,] Cursor = new char[5, 5] {
            {L2,L1,L1,L1,B},
            {L1,L2,L1,B,B},
            {L1,L1,L2,B,B},
            {L1,B,B,L1,B},
            {B,B,B,B,L1,}};

                //public char[,] Select = new char[5, 5] {
                // {L2,L2,L2,L2,B},
                // {L2,L2,B,B,B},
                // {L2,B,L2,L2,L2},
                // {L2,B,L2,L2,B},
                // {B,B,L2,B,L2}};

                public char[,] Select = new char[5, 5] {
            {L5,L1,L1,L1,B},
            {L1,L5,L1,B,B},
            {L1,L1,L5,B,B},
            {L1,B,B,L1,B},
            {B,B,B,B,L1,}};

                public char[,] Dock = new char[5, 5] {
             {L3,L3,L3,L3,L3},
             {L3,B,B,B,L3},
             {L3,B,L3,B,L3},
             {B,B,L3,B,B},
             {B,B,B,B,B}};

                public char[,] StdBrd = new char[5, 5] {
             {L4,L4,L4,L4,L4},
             {L4,L4,B,B,L4},
             {L4,B,L4,B,L4},
             {L4,L4,B,L4,L4},
             {B,L4,L4,L4,B}};


            } // Standard Cursor Details
            CURSOR_SYMBOLOGY SYST_CURSOR = new CURSOR_SYMBOLOGY();

            class SHIP_SYMBOLS
            {
                public static char[,] Cruiser = new char[3, 7] {
            { P, P, P, P, P, P, B, },
            { B, P, B, B, B, P, P, },
            { P, P, P, P, P, P, B, }};

                public static char[,] Carrier = new char[3, 7] {
            { P, P, P, P, P, P, P, },
            { P, B, B, B, B, B, P, },
            { P, P, P, P, P, P, P, }};

                public static char[,] Basic_Frigate = new char[3, 7] {
            { P, P, P, P, P, B, B, },
            { B, P, B, B, B, P, B, },
            { B, B, P, P, P, P, P, }};

                public static char[,] HeavyMissile_Frigate = new char[3, 7] {
            { P, P, P, P, P, B, B, },
            { B, P, P, B, P, P, B, },
            { B, B, P, P, P, P, P, }};

                public static char[,] GravCannon_Frigate = new char[3, 7] {
            { P, P, P, P, P, B, B, },
            { B, P, P, B, P, P, B, },
            { B, B, P, P, P, P, P, }};

                public static char[,] Intercept = new char[3, 7] {
            { B, B, B, P, B, B, B, },
            { B, B, P, B, P, B, B, },
            { B, B, B, B, B, B, B, }};

                public static char[,] PrdCarrier = new char[3, 7] {
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, }};

                public static char[,] Bomber = new char[3, 7] {
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, }};

                public static char[,] MOTH = new char[3, 7] {
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, },
            { B, B, B, B, B, B, B, }};

                public Dictionary<string, char[,]> SYST_SYMBLS_PROCEDURAL = new Dictionary<string, char[,]>()
            {{ "CR",Cruiser},
            { "CA",Carrier},
            { "FR",Basic_Frigate},
            { "FG",GravCannon_Frigate},
            { "FM",HeavyMissile_Frigate},
            { "MOTH",MOTH},
            { "IN",Intercept}};

            } // Standard Ship Symbols (11X5, 6,3 IS CENTRE)
            SHIP_SYMBOLS SYST_SYMBLS = new SHIP_SYMBOLS();

            class LETTERING
            {
                public char[,] SELECT = new char[5, 23] {
            {P,P,P,B,P,P,P,B,P,B,B,B,P,P,P,B,P,P,P,B,P,P,P},
            {P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,B,P,B},
            {P,P,P,B,P,P,P,B,P,B,B,B,P,P,P,B,P,B,B,B,B,P,B},
            {B,B,P,B,P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,B,P,B},
            {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,B,P,B}};

                public char[,] GOTO = new char[5, 23] {
            {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,B,B,B,B,B,B,B},
            {P,B,B,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,P,P,B,P,P,P,B,B,P,B,B,P,P,P,B,B,B,B,B,B,B,B}};

                public char[,] FOLLOW = new char[5, 23] {
            {P,P,P,B,P,P,P,B,P,B,B,P,B,B,P,P,P,B,P,B,B,B,P},
            {P,B,B,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,B,B,B,P},
            {P,P,P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,B,P,B,P},
            {P,B,B,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,P,B,P,P},
            {P,B,B,B,P,P,P,B,P,P,B,P,P,B,P,P,P,B,P,B,B,B,P}};

                public char[,] DOCK = new char[5, 23] {
            {P,P,P,B,P,P,P,B,P,P,P,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,B,P,B,P,B,P,B,P,B,B,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,B,P,B,P,B,P,B,P,B,B,B,P,P,B,B,B,B,B,B,B,B,B},
            {P,B,P,B,P,B,P,B,P,B,B,B,P,B,P,B,B,B,B,B,B,B,B},
            {P,P,P,B,P,P,P,B,P,P,P,B,P,B,P,B,B,B,B,B,B,B,B}};

                public char[,] ATTACK = new char[5, 23] {
            {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,B,P},
            {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,B,B,B,P,B,P},
            {P,P,P,B,B,P,B,B,B,P,B,B,P,P,P,B,P,B,B,B,P,P,B},
            {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,B,B,B,P,B,P},
            {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,P,P,B,P,B,P}};

            } // Standard Ship Symbols (11X5, 6,3 IS CENTRE)
            LETTERING SYST_LETTERING = new LETTERING();

            //Permanently Logged Blocks
            //-----------------------------------
            IMyTextPanel DISPLAY_PANEL_TL; // Top Left
            IMyTextPanel DISPLAY_PANEL_TR; // Top Right
            IMyTextPanel DISPLAY_PANEL_BL; // Bottom Left
            IMyTextPanel DISPLAY_PANEL_BR; // Bottom Right
            IMyProgrammableBlock COMMAND_MODULE;
            IMyShipController CONTROL;
            List<IMyGyro> GYROS = new List<IMyGyro>();

            //User Interface Stored Data
            //-------------------------------------
            List<string> SELECTED_SQDS = new List<string>();
            int UI_SCALE = 20; //UI scale in meters per pixel
            int OFFSETY = 100; //Initial position of me on screen (doubled for 200x200)
            int OFFSETX = 100; //Initial position of me on screen (doubled for 200x200)
            bool PREV_CLICK = false; //Was previously Clicking
            char[,] MOUSE_SYMB;
            string FR_HOVER;
            string EN_HOVER;
            char[,] TEXT_OUT;
            Vector2D STARTCLICK; //Starting click position for box dragging
            Vector2D CLICKPOS; //position of last command
            int CLICK_TIMER = 0; //Timer for the clickmarker


            //User Interface Screen Limits (Updated for 200x200)
            //--------------------------------
            double POS_Y = 100; //Mouse X pos (doubled for 200x200)
            double POS_X = 100; //Mouse YPos (doubled for 200x200)
            const int ROWS_CT = 200;  //Res Vertical (doubled for 200x200)
            const int COLUMNS_CT = 200; //Res Horizontal (doubled for 200x200)
            char[] ALLOC_TL = new char[10101]; //Screen Size for Top Left panel
            char[] ALLOC_TR = new char[10101]; //Screen Size for Top Right panel
            char[] ALLOC_BL = new char[10101]; //Screen Size for Bottom Left panel
            char[] ALLOC_BR = new char[10101]; //Screen Size for Bottom Right panel
            int ROW_UI_START = 14; //Doubled for 200x200
            int ROW_UI_END = 188; //Doubled for 200x200
            int ROW_ZOOM_ICON = 8; //Doubled for 200x200
            int COL_ZOOM_ICON1 = 156; //Doubled for 200x200
            int COL_ZOOM_ICON2 = 192; //Doubled for 200x200
            int SYST_PROXIMITY = 6; //Doubled for 200x200
            int SYST_NUMBERS_ROW = 4; //Doubled for 200x200
            int SYST_NUMBERS_COL = 164; //Doubled for 200x200


            //System Initialisation
            //--------------------------
            bool UIINIT_HASRUN = false;
            bool SYSTINIT_HASRUN = false;

            //System Saves
            //---------------------
            //StringBuilder VISUALDATA = new StringBuilder();
            String VISUALDATA = "";

            #endregion

            // Constructor to set up IGC system
            public Program()
            {
                // Set up IGC listeners for fleet communication
                IGCListener = IGC.RegisterBroadcastListener(IGCTagIN);

                // Set script to run every 10 ticks for better performance
                Runtime.UpdateFrequency = UpdateFrequency.Update1;

                // Initialize the display system
                Echo("Fleet Display Script Ver_008_Updated_2x2LCD Initialized");
                Echo("IGC Communication System Active");
                Echo("Listening on channel: " + IGCTagIN);
                Echo("Broadcasting on channel: " + IGCTagOUT);
                Echo("2x2 LCD Array Mode - 200x200 Total Display");
            }

            #region MAIN USER INTERFACE
            /*=======================================================================================
              Function: User Interface
              ---------------------------------------
              function will: Take user inputs and convert them to a usable command structure
            //=======================================================================================*/
            int TIMER;
            void Main(string argument, UpdateType updateSource)
            {
                try
                {
                    // Handle IGC messages from fleet
                    if (IGCListener != null && IGCListener.HasPendingMessage)
                    {
                        var message = IGCListener.AcceptMessage();
                        if (message.Data != null)
                        {
                            argument = message.Data.ToString();
                            Echo($"Received IGC message: {argument.Substring(0, Math.Min(50, argument.Length))}...");
                            // Process fleet communication data
                            PROCESS_FLEET_DATA(argument);
                        }
                    }

                    //Initialisation 0.25
                    //---------------------------
                    if (UIINIT_HASRUN == false)
                    { UIINIT(); UIINIT_HASRUN = true; }
                    //Echo(Runtime.LastRunTimeMs + "");
                    try
                    {
                        if (SYSTINIT_HASRUN == false)
                        { FC_INIT(); SYSTINIT_HASRUN = true; }
                        //if (TIMER == 1)
                        { SYST_UPDATE(); }
                    }
                    catch (Exception e)
                    {
                        Echo("Display Script Error: " + e.Message);
                        Echo("Stack Trace: " + e.StackTrace);
                    }

                    OP_BAR();
                    Echo(VERSION);

                    //All Follow Me Command Issuer
                    //------------------------------
                    if (argument == "ALLFOLLOWME") //All follow me issued
                    {
                        foreach (var DRONE in DRONES)
                        { DRONE.Value.COMLOC = "FOLLOW^ME"; }
                        FC_SAVE();
                        return;
                    }
