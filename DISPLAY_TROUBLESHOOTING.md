# FleetDisplayScript2_QuadLCD Display Troubleshooting Guide

## Current Issues Fixed

### 1. Corrected Quadrant Coordinates
- **Fixed**: Rendering coordinates now properly use 200x200 ranges
- **Before**: Using 400x400 coordinates (0-399)
- **After**: Using 200x200 coordinates (0-199)

### 2. Adjusted Font Size
- **Fixed**: Font size increased from 0.173f to 0.35f for better visibility on 100x100 panels
- **Reason**: Smaller panels need larger font for readability

### 3. Added Debug Information
- **Added**: Debug output showing render counters and panel detection
- **Added**: Test pattern (red diagonal) for first 100 updates to verify display works

### 4. Restored Character Formatting
- **Fixed**: Added basic character replacement for proper LCD display
- **Reason**: LCD panels need proper character spacing

## Troubleshooting Steps

### Step 1: Check LCD Panel Names
Ensure your LCD panels are named exactly:
- `RFC_PANEL_TL` (Top-Left)
- `RFC_PANEL_TR` (Top-Right)
- `RFC_PANEL_BL` (Bottom-Left)
- `RFC_PANEL_BR` (Bottom-Right)

### Step 2: Check Debug Output
Look at the programmable block's output for:
```
Display Update - Counter: X, Draw: Y
Found: TL=True, TR=True, BL=True, BR=True
```

If any panels show `False`, check the naming.

### Step 3: Look for Test Pattern
When you first run the script, you should see a red diagonal test pattern on the displays for the first few seconds. If you don't see this:

1. **Check LCD Panel Settings**:
   - Content Type: Text and Images
   - Font: Monospace
   - Font Size: Should be set automatically to 0.35

2. **Check LCD Panel Arrangement**:
   ```
   [TL] [TR]
   [BL] [BR]
   ```

### Step 4: Verify Control Block
Ensure you have a ship controller named `RFC_RC` on the same grid.

## Expected Display Layout

```
200x200 Total Display:
┌─────────┬─────────┐
│   TL    │   TR    │
│ (0-99,  │ (0-99,  │
│  0-99)  │100-199) │
├─────────┼─────────┤
│   BL    │   BR    │
│(100-199,│(100-199,│
│  0-99)  │100-199) │
└─────────┴─────────┘
```

Each panel shows 100x100 pixels of the total 200x200 display.

## Common Issues and Solutions

### Issue: Blank Displays
**Solution**: 
1. Check panel names are exact
2. Verify panels are on same grid as programmable block
3. Check if script is running (look for debug output)

### Issue: Garbled Text
**Solution**:
1. Set LCD Content Type to "Text and Images"
2. Verify font is set to Monospace
3. Check font size (should be 0.35f)

### Issue: Only Some Panels Working
**Solution**:
1. Check individual panel names
2. Verify all panels are powered
3. Check debug output to see which panels are detected

### Issue: Display Appears Stretched
**Solution**:
1. Verify you're using regular LCD panels, not wide LCDs
2. Check that all 4 panels are the same size
3. Ensure proper physical arrangement

## Performance Notes

- Script now runs at Update10 (6 FPS) instead of Update1 (60 FPS)
- Should use approximately 85-90% fewer instructions
- Display updates every 10 ticks for smooth performance
- Test pattern only shows for first 100 updates to verify functionality

## Next Steps

1. **Compile and run** the updated script
2. **Check debug output** in programmable block
3. **Look for test pattern** on displays
4. **Verify panel detection** in debug messages
5. **Report specific symptoms** if issues persist

If you still see issues, please share:
- What you see on the displays (blank, garbled, partial, etc.)
- Debug output from the programmable block
- Whether the test pattern appears
- Which panels (if any) are working
