/*
 * Fleet Command Quad LCD Display Script - OPTIMIZED VERSION
 * Based on FleetDisplayScript2.cs - Modified for 200x200 display using 4 LCDs
 *
 * PERFORMANCE OPTIMIZATIONS:
 * - Reduced from Update1 to Update10 (6x fewer updates per second)
 * - Proper 200x200 display size (reasonable for instruction limits)
 * - Staggered quadrant rendering (1 panel per update cycle)
 * - Reduced drawing frequency for non-critical elements
 * - Removed expensive string operations
 * - Optimized grid drawing with reduced density
 *
 * Required LCD Panel Names:
 * - RFC_PANEL_TL (Top-Left)
 * - RFC_PANEL_TR (Top-Right)
 * - RFC_PANEL_BL (Bottom-Left)
 * - RFC_PANEL_BR (Bottom-Right)
 *
 * Each panel displays a 100x100 section of the total 200x200 display
 */

public Program()
{
    Runtime.UpdateFrequency = UpdateFrequency.Update10; // Reduced from Update1 to Update10 for performance
    IGCListener = IGC.RegisterBroadcastListener(IGCTagIN);
}

public void Main(string argument, UpdateType updateSource)
{
    try
    {
        if (!SYSTINIT_HASRUN)
        {
            SYSTINIT();
            SYSTINIT_HASRUN = true;
        }

        if (!UIINIT_HASRUN)
        {
            UIINIT();
            UIINIT_HASRUN = true;
        }

        // Main display update for quad LCD system
        MAIN_DISPLAY_UPDATE();
    }
    catch (Exception e)
    {
        Echo($"Main error: {e}");
    }
}

#region Introduction
/*
Introduction
----------------
Hello and thank you for downloading Rdav's Fleet Command Beta (Updated for Current SE)
Rdav's fleet command is a total-conversion code for automatic fleets.
This code allows for artificially intelligent drone operation, drones
adopt unique and intelligent behaviour allowing them to engage targets
follow commands, dock, undock along with giving players advanced
control capabilities for an entire armada.
Please see the workshop page for more details;

You are currently looking at the 'Central Command' Unit, which
is the main hub of operations for the code, only ONE of these units
should be used per-fleet, others can be kept as backups, but only one
should be operational at any one time.

Rdav 28/08/17
Updated for current Space Engineers API - 2024
Modified for Quad LCD 200x200 Display - 2024

Installation
--------------
The code should come in a Pre-Fab format to make installation a breeze.
Setup of these modules manually can be achieved however for streamlining
or otherwise making the components of the module smaller, please refer to
the manual to do so.

The Central Command Unit will automatically:
 * Use IGC (Inter-Grid Communication) for fleet communication
 * Find a command seat used for issuing commands called 'RFC_RC' (only renaming required)
 * Find and use for target detection any turret with appropriate naming
 * Use 4 LCD panels for a 200x200 display

Required LCD Panel Names:
 * RFC_PANEL_TL (Top-Left quadrant)
 * RFC_PANEL_TR (Top-Right quadrant)
 * RFC_PANEL_BL (Bottom-Left quadrant)
 * RFC_PANEL_BR (Bottom-Right quadrant)

Bughunting/Problems
--------------------
The code will automatically create a bugreport and save it to the custom-data of the
designated remote block.

Suggestions Planned Features
-----------------------------
- Enhanced fleet coordination features
- Improved UI responsiveness
- 200x200 display for tactical overview

 ChangeLog:
 * Updated to use IGC (Inter-Grid Communication) system
 * Removed deprecated antenna communication methods
 * Updated for current Space Engineers API compatibility
 * Modified for Quad LCD 200x200 display system
 * Enhanced UI scaling for larger display
 * 18/01/2017 ln255 Added support for alternate orientations.
 * 18/01/2017 DETECT changed convergance to 100-800m default
 * added command recieved indicator to LCD
 * updated command recieved indicator
 * Fixed UI unresponsiveness
 * Updated to reflect self ship docking
 * Updated to draw a line for a go-to
*/

#endregion

#region System Constants

//Display Setup - Quad LCD Configuration
//---------------------------------------------------------
string VERSION = "Ver_008_QuadLCD";                         //Script Current Version

//IGC Communication Setup
//---------------------------------------------------------
const string IGCTagOUT = "RFC_FLEET_OUT";
const string IGCTagIN = "RFC_FLEET_IN";
IMyBroadcastListener IGCListener;

// Color Constants
const char P = medBlue; //Primary System Colour (your own ships)
const char B = ' '; //Background Colour
const char L1 = black; //Layer1background1colour
const char L2 = mediumGray; //Layer2background1colour

// Quad LCD Display Arrays (200x200 total display)
public char[,] BDRAW = new char[ROWS_CT, COLUMNS_CT]; //Stores Background
char[,] DRAW = new char[ROWS_CT, COLUMNS_CT]; //Temporary Assigner

// Quad LCD Screen Dimensions (4 panels of 100x100 each)
const int ROWS_CT = 200;         // Total height: 200 pixels
const int COLUMNS_CT = 200;      // Total width: 200 pixels
const int PANEL_SIZE = 100;      // Each panel is 100x100
char[] ALLOC_TL = new char[10101]; // Top-Left panel buffer (100*100 + 100 newlines + 1)
char[] ALLOC_TR = new char[10101]; // Top-Right panel buffer
char[] ALLOC_BL = new char[10101]; // Bottom-Left panel buffer
char[] ALLOC_BR = new char[10101]; // Bottom-Right panel buffer

#region Letter Codes
const char red = '\uE200';
const char medRed = '\uE1C0';
const char darkRed = '\uE180';

const char green = '\uE120';
const char medGreen = '\uE118';
const char darkGreen = '\uE110';

const char blue = '\uE104';
const char medBlue = '\uE103';
const char darkBlue = '\uE102';

const char yellow = '\uE220';
const char medYellow = '\uE1D8';
const char darkYellow = '\uE190';

const char magenta = '\uE204';
const char medMagenta = '\uE1C3';
const char darkMagenta = '\uE182';

const char cyan = '\uE124';
const char medCyan = '\uE11B';
const char darkCyan = '\uE112';

const char white = '\uE2FF';
const char lightGray = '\uE1DB';
const char mediumGray = '\uE192';
const char darkGray = '\uE149';
const char black = '\uE100';
#endregion

//System Data Management
//------------------------
class DC_INF_INFO
{
    public int SIZE; //Size of target
    public string TYPE; //Type of target
    public Vector3D POSITION; //Position
    public string DIRECT_TO_OUTPUT; //Directly Outputted string
    public int ST_SIZE; //Start Size
    public Vector2D UIPOS; // Position of UI locator
}

class DRONE_INFO
{
    public string ID; //Drone Identifier (contains classification)
    public string COMLOC; //Drone Command & locator
    public string GLOC; //Generated Locator
    public Vector3D LOC; //Current Drone Location
    public Vector3D VEL; //Drone Velocity
    public Vector3D TVEl; //Target Velocity vector
    public string ISDOCKED; //id of docked ship (own Id if not docked)
    public double HEALTH; //Health of the ship
    public DateTime LAST_PING; //Last recieved ping from the ship
    public string EXT_INF; //Drone Extra Info
    public string OUTPUT;   // String Drone Data Output
    public Vector2D UIPOS; // Position of UI locator (UI Only)

    //Standardised System Of Updating And Saving Drone Data
    public static DRONE_INFO DRONE_DATA_RS(string IN_ARG, DRONE_INFO DRONE_INF, bool[] RUN_ID)
    {
        //Retrieves Data From Store
        string[] DRN_INFO = IN_ARG.Split('*');
        DRONE_INF.ID = (RUN_ID[0] != true) ? DRONE_INF.ID : DRN_INFO[0];
        DRONE_INF.COMLOC = (RUN_ID[1] != true) ? DRONE_INF.COMLOC : DRN_INFO[1];
        DRONE_INF.GLOC = (RUN_ID[2] != true) ? DRONE_INF.GLOC : DRN_INFO[2];
        if (RUN_ID[3] == true) { Vector3D.TryParse(DRN_INFO[3], out DRONE_INF.LOC); }
        if (RUN_ID[4] == true) { Vector3D.TryParse(DRN_INFO[4], out DRONE_INF.VEL); }
        if (RUN_ID[5] == true) { Vector3D.TryParse(DRN_INFO[5], out DRONE_INF.TVEl); }
        if (RUN_ID[6] == true) { DRONE_INF.ISDOCKED = DRN_INFO[6]; }
        if (RUN_ID[7] == true) { DRONE_INF.HEALTH = double.Parse(DRN_INFO[7]); }
        if (RUN_ID[8] == true) { DRONE_INF.LAST_PING = DateTime.Parse(DRN_INFO[8]); }
        if (RUN_ID[9] == true) { DRONE_INF.EXT_INF = DRN_INFO[9]; }
        return DRONE_INF;
    }

    public static DRONE_INFO SAVE(DRONE_INFO DRONE_INF)
    {
        DRONE_INF.OUTPUT = string.Join("*", "#" + DRONE_INF.ID, DRONE_INF.COMLOC, DRONE_INF.GLOC, DRONE_INF.LOC, DRONE_INF.VEL, DRONE_INF.TVEl, DRONE_INF.ISDOCKED, DRONE_INF.HEALTH, DRONE_INF.LAST_PING, DRONE_INF.EXT_INF, "#" + DRONE_INF.ID);
        return DRONE_INF;
    }
}

class DOCKPOINT_INFO
{
    public string ID; //Dockpoint Identifier (contains docktype classification)
    public Vector3D LOC; //Current Dockpoint Location
    public string BASE_TAG; //ID of base ship
    public string ISDOCKED; //Id of docked ship (own Id if not docked)
    public DateTime LAST_PING; //Last recieved ping from the dockpoint
    public string OUTPUTROLL; //Coordinates package for drones to interperate
    public string OUTPUT;   // String Drone Data Output

    public List<IMyTerminalBlock> ROUTE; //List of route (drone ship only, not updated)

    //Standardised System Of Updating And Saving Drone Data
    public static DOCKPOINT_INFO DOCK_DATA_RS(string IN_ARG, DOCKPOINT_INFO DOCKPT_INF, bool[] RUN_ID)
    {
        //Retrieves Data From Store
        string[] DCK_INFO = IN_ARG.Split('*');
        if (RUN_ID[0] == true) { DOCKPT_INF.ID = DCK_INFO[0]; }
        if (RUN_ID[1] == true) { Vector3D.TryParse(DCK_INFO[1], out DOCKPT_INF.LOC); }
        if (RUN_ID[2] == true) { DOCKPT_INF.BASE_TAG = DCK_INFO[2]; }
        if (RUN_ID[3] == true) { DOCKPT_INF.ISDOCKED = DCK_INFO[3]; }
        if (RUN_ID[4] == true) { DOCKPT_INF.LAST_PING = DateTime.Parse(DCK_INFO[4]); }
        if (RUN_ID[5] == true) { DOCKPT_INF.OUTPUTROLL = DCK_INFO[5]; }

        DOCKPT_INF.OUTPUT = string.Join("*", "#" + DOCKPT_INF.ID, DOCKPT_INF.LOC, DOCKPT_INF.BASE_TAG, DOCKPT_INF.ISDOCKED, DOCKPT_INF.LAST_PING, DOCKPT_INF.OUTPUTROLL, "#" + DOCKPT_INF.ID);
        return DOCKPT_INF;
    }

    //Standardised DockString Saving Procedure
    public static DOCKPOINT_INFO SAVE_ROUTE_TO_STRING(DOCKPOINT_INFO DOCKPT_INFO)
    {
        List<string> OUTPUT = new List<string>();
        double OFFSET_CONST = 2;
        List<IMyTerminalBlock> DOCKPT_TRAIL = DOCKPT_INFO.ROUTE;

        //Adds First Ordinates (self and forwards position)
        OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (1.5), 2) + "");
        OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[0].GetPosition() + DOCKPT_TRAIL[0].WorldMatrix.Forward * (OFFSET_CONST + 2.5), 2) + "");

        //Iterates Through List Of LCD's
        for (int i = 1; i < DOCKPT_TRAIL.Count; i++)
        { var IMYPLACE = DOCKPT_TRAIL[i]; OUTPUT.Add(Vector3D.Round(IMYPLACE.GetPosition() + IMYPLACE.WorldMatrix.Backward * OFFSET_CONST, 2) + ""); }

        //Adds Final Position
        OUTPUT.Add(Vector3D.Round(DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].GetPosition() +
            DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Backward * OFFSET_CONST + DOCKPT_TRAIL[DOCKPT_TRAIL.Count - 1].WorldMatrix.Up * 100, 2) + "");

        //Saves To String, Updates Locator, (And Updates OUTPUT)
        DOCKPT_INFO.OUTPUTROLL = string.Join("^", OUTPUT);
        DOCKPT_INFO.LOC = Vector3D.Round(DOCKPT_TRAIL[0].GetPosition(), 2);
        DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);

        return DOCKPT_INFO;
    }

    public static DOCKPOINT_INFO SAVE(DOCKPOINT_INFO DOCKPT_INFO)
    {
        DOCKPT_INFO.OUTPUT = string.Join("*", "#" + DOCKPT_INFO.ID, DOCKPT_INFO.LOC, DOCKPT_INFO.BASE_TAG, DOCKPT_INFO.ISDOCKED, DOCKPT_INFO.LAST_PING, DOCKPT_INFO.OUTPUTROLL, "#" + DOCKPT_INFO.ID);
        return DOCKPT_INFO;
    }
}

Dictionary<string, DC_INF_INFO> DECENTIN_INFO = new Dictionary<string, DC_INF_INFO>();
Dictionary<string, DOCKPOINT_INFO> DOCKPOINTS = new Dictionary<string, DOCKPOINT_INFO>();
Dictionary<string, DRONE_INFO> DRONES = new Dictionary<string, DRONE_INFO>();

//Symbology Setup
//-------------------------------------------
class FIVE_THREE_NUMBERS
{
    static char N = blue;
    static char[,] NUM_0 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, B, N }, { N, B, N }, { N, N, N } };
    static char[,] NUM_1 = new char[5, 3] { { N, N, B }, { B, N, B }, { B, N, B }, { B, N, B }, { N, N, N } };
    static char[,] NUM_2 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { N, B, B }, { N, N, N } };
    static char[,] NUM_3 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { B, B, N }, { N, N, N } };
    static char[,] NUM_4 = new char[5, 3] { { N, B, B }, { N, B, B }, { N, B, N }, { N, N, N }, { B, B, N } };
    static char[,] NUM_5 = new char[5, 3] { { N, N, N }, { N, B, B }, { N, N, N }, { B, B, N }, { N, N, N } };
    static char[,] NUM_6 = new char[5, 3] { { N, N, N }, { N, B, B }, { N, N, N }, { N, B, N }, { N, N, N } };
    static char[,] NUM_7 = new char[5, 3] { { N, N, N }, { B, B, N }, { N, N, N }, { B, N, B }, { B, N, B } };
    static char[,] NUM_8 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, N, N }, { N, B, N }, { N, N, N } };
    static char[,] NUM_9 = new char[5, 3] { { N, N, N }, { N, B, N }, { N, N, N }, { B, B, N }, { N, N, N } };

    public char[][,] NUMBERS = new char[][,] { NUM_0, NUM_1, NUM_2, NUM_3, NUM_4, NUM_5, NUM_6, NUM_7, NUM_8, NUM_9 };  //list of Numbers for external accessor
} // Standard Numbers
FIVE_THREE_NUMBERS SYST_NBRS = new FIVE_THREE_NUMBERS();

class CURSOR_SYMBOLOGY
{
    const char L1 = black;
    const char L2 = darkYellow;
    const char L3 = darkMagenta;
    const char L4 = cyan;
    const char L5 = green;
    const char L6 = red;

    public char[,] Attack = new char[5, 5] {
        {L6,L1,L1,L1,B},
        {L1,L6,L1,B,B},
        {L1,L1,L6,B,B},
        {L1,B,B,L1,B},
        {B,B,B,B,L1,}};

    public char[,] GoTo = new char[5, 5] {
        {L1,B,L1,B,B},
        {B,L1,B,L1,B},
        {B,L1,B,L1,B},
        {L1,B,L1,B,B},
        {B,B,B,B,B}};

    public char[,] Cursor = new char[5, 5] {
        {L2,L1,L1,L1,B},
        {L1,L2,L1,B,B},
        {L1,L1,L2,B,B},
        {L1,B,B,L1,B},
        {B,B,B,B,L1,}};

    public char[,] Select = new char[5, 5] {
        {L5,L1,L1,L1,B},
        {L1,L5,L1,B,B},
        {L1,L1,L5,B,B},
        {L1,B,B,L1,B},
        {B,B,B,B,L1,}};

    public char[,] Dock = new char[5, 5] {
         {L3,L3,L3,L3,L3},
         {L3,B,B,B,L3},
         {L3,B,L3,B,L3},
         {B,B,L3,B,B},
         {B,B,B,B,B}};

    public char[,] StdBrd = new char[5, 5] {
         {L4,L4,L4,L4,L4},
         {L4,L4,B,B,L4},
         {L4,B,L4,B,L4},
         {L4,L4,B,L4,L4},
         {B,L4,L4,L4,B}};

} // Standard Cursor Details
CURSOR_SYMBOLOGY SYST_CURSOR = new CURSOR_SYMBOLOGY();

class SHIP_SYMBOLS
{
    public static char[,] Cruiser = new char[3, 7] {
        { P, P, P, P, P, P, B, },
        { B, P, B, B, B, P, P, },
        { P, P, P, P, P, P, B, }};

    public static char[,] Carrier = new char[3, 7] {
        { P, P, P, P, P, P, P, },
        { P, B, B, B, B, B, P, },
        { P, P, P, P, P, P, P, }};

    public static char[,] Basic_Frigate = new char[3, 7] {
        { P, P, P, P, P, B, B, },
        { B, P, B, B, B, P, B, },
        { B, B, P, P, P, P, P, }};

    public static char[,] HeavyMissile_Frigate = new char[3, 7] {
        { P, P, P, P, P, B, B, },
        { B, P, P, B, P, P, B, },
        { B, B, P, P, P, P, P, }};

    public static char[,] GravCannon_Frigate = new char[3, 7] {
        { P, P, P, P, P, B, B, },
        { B, P, P, B, P, P, B, },
        { B, B, P, P, P, P, P, }};

    public static char[,] Intercept = new char[3, 7] {
        { B, B, B, P, B, B, B, },
        { B, B, P, B, P, B, B, },
        { B, B, B, B, B, B, B, }};

    public static char[,] PrdCarrier = new char[3, 7] {
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, }};

    public static char[,] Bomber = new char[3, 7] {
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, }};

    public static char[,] MOTH = new char[3, 7] {
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, },
        { B, B, B, B, B, B, B, }};

    public Dictionary<string, char[,]> SYST_SYMBLS_PROCEDURAL = new Dictionary<string, char[,]>()
        {{ "CR",Cruiser},
        { "CA",Carrier},
        { "FR",Basic_Frigate},
        { "FG",GravCannon_Frigate},
        { "FM",HeavyMissile_Frigate},
        { "MOTH",MOTH},
        { "IN",Intercept}};

} // Standard Ship Symbols (11X5, 6,3 IS CENTRE)
SHIP_SYMBOLS SYST_SYMBLS = new SHIP_SYMBOLS();

class LETTERING
{
    public char[,] SELECT = new char[5, 23] {
        {P,P,P,B,P,P,P,B,P,B,B,B,P,P,P,B,P,P,P,B,P,P,P},
        {P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,B,P,B},
        {P,P,P,B,P,P,P,B,P,B,B,B,P,P,P,B,P,B,B,B,B,P,B},
        {B,B,P,B,P,B,B,B,P,B,B,B,P,B,B,B,P,B,B,B,B,P,B},
        {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,B,P,B}};

    public char[,] GOTO = new char[5, 23] {
        {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,B,B,B,B,B,B,B},
        {P,B,B,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,P,P,B,P,P,P,B,B,P,B,B,P,P,P,B,B,B,B,B,B,B,B}};

    public char[,] FOLLOW = new char[5, 23] {
        {P,P,P,B,P,P,P,B,P,B,B,P,B,B,P,P,P,B,P,B,B,B,P},
        {P,B,B,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,B,B,B,P},
        {P,P,P,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,B,P,B,P},
        {P,B,B,B,P,B,P,B,P,B,B,P,B,B,P,B,P,B,P,P,B,P,P},
        {P,B,B,B,P,P,P,B,P,P,B,P,P,B,P,P,P,B,P,B,B,B,P}};

    public char[,] DOCK = new char[5, 23] {
        {P,P,P,B,P,P,P,B,P,P,P,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,B,P,B,P,B,P,B,P,B,B,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,B,P,B,P,B,P,B,P,B,B,B,P,P,B,B,B,B,B,B,B,B,B},
        {P,B,P,B,P,B,P,B,P,B,B,B,P,B,P,B,B,B,B,B,B,B,B},
        {P,P,P,B,P,P,P,B,P,P,P,B,P,B,P,B,B,B,B,B,B,B,B}};

    public char[,] ATTACK = new char[5, 23] {
        {P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,P,P,B,P,B,P},
        {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,B,B,B,P,B,P},
        {P,P,P,B,B,P,B,B,B,P,B,B,P,P,P,B,P,B,B,B,P,P,B},
        {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,B,B,B,P,B,P},
        {P,B,P,B,B,P,B,B,B,P,B,B,P,B,P,B,P,P,P,B,P,B,P}};

} // Standard Ship Symbols (11X5, 6,3 IS CENTRE)
LETTERING SYST_LETTERING = new LETTERING();

//Permanently Logged Blocks - Quad LCD System
//-----------------------------------
IMyTextPanel DISPLAY_PANEL_TL; // Top-Left
IMyTextPanel DISPLAY_PANEL_TR; // Top-Right
IMyTextPanel DISPLAY_PANEL_BL; // Bottom-Left
IMyTextPanel DISPLAY_PANEL_BR; // Bottom-Right
IMyProgrammableBlock COMMAND_MODULE;
IMyShipController CONTROL;
List<IMyGyro> GYROS = new List<IMyGyro>();

//User Interface Stored Data - Scaled for 200x200
//-------------------------------------
List<string> SELECTED_SQDS = new List<string>();
int UI_SCALE = 20; //UI scale in meters per pixel
double OFFSETY = 100; //Initial position of me on screen (centered for 200x200)
double OFFSETX = 100; //Initial position of me on screen (centered for 200x200)
bool PREV_CLICK = false; //Was previously Clicking
char[,] MOUSE_SYMB;
string FR_HOVER = "999";
string EN_HOVER = "999";
char[,] TEXT_OUT;
Vector2D STARTCLICK; //Starting click position for box dragging
Vector2D CLICKPOS; //position of last command
int CLICK_TIMER = 0; //Timer for the clickmarker

//User Interface Screen Limits - Scaled for 200x200
//--------------------------------
double POS_Y = 100; //Mouse X pos (centered)
double POS_X = 100; //Mouse Y pos (centered)
int ROW_UI_START = 10;    // Scaled for 200x200
int ROW_UI_END = 190;     // Scaled for 200x200
int ROW_ZOOM_ICON = 7;    // Scaled for 200x200
int COL_ZOOM_ICON1 = 160; // Scaled for 200x200
int COL_ZOOM_ICON2 = 190; // Scaled for 200x200
int SYST_PROXIMITY = 6;   // Scaled for 200x200
int SYST_NUMBERS_ROW = 4; // Scaled for 200x200
int SYST_NUMBERS_COL = 170; // Scaled for 200x200

//System Initialisation
//--------------------------
bool UIINIT_HASRUN = false;
bool SYSTINIT_HASRUN = false;
int TIMER = 0;
int RENDER_COUNTER = 0; // Counter to reduce rendering frequency
int DRAW_COUNTER = 0;   // Counter to reduce drawing frequency

//System Saves
//---------------------
String VISUALDATA = "";

// Input handling
bool ISLEFT_CLICKING = false;
bool ISRIGHT_CLICKING = false;

#endregion

#region Main Display Update
/*=======================================================================================
  Function: Main Display Update for Quad LCD System
  ---------------------------------------
  function will: Handle the main display rendering for 200x200 quad LCD system
//=======================================================================================*/
void MAIN_DISPLAY_UPDATE()
{
    try
    {
        // Check if required blocks are available
        if (DISPLAY_PANEL_TL == null || DISPLAY_PANEL_TR == null ||
            DISPLAY_PANEL_BL == null || DISPLAY_PANEL_BR == null)
        {
            Echo("Missing LCD Panels - Need: RFC_PANEL_TL, RFC_PANEL_TR, RFC_PANEL_BL, RFC_PANEL_BR");
            Echo($"Found: TL={DISPLAY_PANEL_TL != null}, TR={DISPLAY_PANEL_TR != null}, BL={DISPLAY_PANEL_BL != null}, BR={DISPLAY_PANEL_BR != null}");
            return;
        }

        if (CONTROL == null)
        {
            Echo("Missing Control Block - Need: RFC_RC");
            return;
        }

        // Debug info
        Echo($"Display Update - Counter: {RENDER_COUNTER}, Draw: {DRAW_COUNTER}");

        // Add test pattern to verify display is working
        if (RENDER_COUNTER < 100) // Only for first few seconds
        {
            for (int i = 0; i < 10; i++)
            {
                for (int j = 0; j < 10; j++)
                {
                    if (i + j < ROWS_CT && i + j < COLUMNS_CT)
                        DRAW[i + j, i + j] = red; // Diagonal test pattern
                }
            }
        }

        if (CONTROL.IsUnderControl)  //Only if under control
        {
            //Sets Gyros to avoid spinning a ship while under control
            foreach (var item in GYROS)
            { item.GyroOverride = true; }

            //Generates Mouse Pos (scaled for 200x200)
            POS_Y = MathHelper.Clamp(POS_Y + (CONTROL.RotationIndicator.X) * 0.2, 0, COLUMNS_CT - 1);
            POS_X = MathHelper.Clamp(POS_X + (CONTROL.RotationIndicator.Y) * 0.2, 0, ROWS_CT - 1);

            //Clamps Values
            MathHelper.Clamp(POS_Y, 0, COLUMNS_CT - 1);
            MathHelper.Clamp(POS_X, 0, ROWS_CT - 1);

            //Click Reader
            ISLEFT_CLICKING = CONTROL.RollIndicator < 0;
            ISRIGHT_CLICKING = CONTROL.RollIndicator > 0;
        }

        //Preliminary Assignment
        //------------------------
        MOUSE_SYMB = SYST_CURSOR.Cursor;
        TEXT_OUT = SYST_LETTERING.SELECT;

        //Primary Drawer - Reduced frequency to save instructions
        //----------------------------------------------
        if (DRAW_COUNTER % 3 == 0) // Only draw objects every 3rd update
        {
            foreach (var item in DECENTIN_INFO) //Draws Items Of Interest
            { WRT_DEI(item.Value); }
            var KEYS = new List<string>(DRONES.Keys);
            for (int i = 0; i < DRONES.Count; i++) //draws Squadrons
            { DRONES[KEYS[i]] = WRT_SQD(DRONES[KEYS[i]]); }
        }

        //Secondary Drawer - Always draw UI elements
        //----------------------------------------------
        if (DRAW_COUNTER % 2 == 0) // Draw chart every 2nd update
        { DRAW_CHART(); }
        WRT_SYMB(MOUSE_SYMB, (int)POS_X, (int)POS_Y); //Draws Mouse
        WRT_SYMB(TEXT_OUT, 10, 10); //Draws Text

        //Click Timer
        //----------------------------------------------
        if (CLICK_TIMER > 0)
        { CLICK_TIMER--; }

        // Draw current position indicator (for 200x200)
        Vector2D ME_LOC_LO = new Vector2D(OFFSETX, OFFSETY);
        RASTER(ref ME_LOC_LO);
        if (ME_LOC_LO.X > 5 && ME_LOC_LO.X < COLUMNS_CT - 5 && ME_LOC_LO.Y > 25 && ME_LOC_LO.Y < ROWS_CT - 25)
        {
            // Draw larger position indicator for massive display
            for (int i = -2; i <= 2; i++)
            {
                for (int j = -2; j <= 2; j++)
                {
                    int drawY = (int)ME_LOC_LO.Y + i;
                    int drawX = (int)ME_LOC_LO.X + j;
                    if (drawY >= 0 && drawY < ROWS_CT && drawX >= 0 && drawX < COLUMNS_CT)
                        DRAW[drawY, drawX] = blue;
                }
            }
        }

        // Increment counters
        DRAW_COUNTER++;
        RENDER_COUNTER++;

        // Render to Quad LCD system every update for proper display
        RENDER_TO_QUAD_LCD();
    }
    catch (Exception e)
    {
        Echo($"Error in display update: {e}");
    }
}
#endregion

#region Quad LCD Rendering System
/*=======================================================================================
  Function: Quad LCD Rendering System
  ---------------------------------------
  function will: Render the 200x200 display across 4 LCD panels
//=======================================================================================*/
void RENDER_TO_QUAD_LCD()
{
    try
    {
        // Render all quadrants but less frequently for better performance
        // Top-Left Panel (0-99, 0-99)
        RenderQuadrant(DISPLAY_PANEL_TL, ALLOC_TL, 0, 0, "TL");

        // Top-Right Panel (0-99, 100-199)
        RenderQuadrant(DISPLAY_PANEL_TR, ALLOC_TR, 0, 100, "TR");

        // Bottom-Left Panel (100-199, 0-99)
        RenderQuadrant(DISPLAY_PANEL_BL, ALLOC_BL, 100, 0, "BL");

        // Bottom-Right Panel (100-199, 100-199)
        RenderQuadrant(DISPLAY_PANEL_BR, ALLOC_BR, 100, 100, "BR");

        // Reset draw buffer
        DRAW = BDRAW.Clone() as char[,];
        TIMER = 0;
    }
    catch (Exception e)
    {
        Echo($"Error rendering quad LCD: {e}");
    }
}

void RenderQuadrant(IMyTextPanel panel, char[] buffer, int startRow, int startCol, string quadrant)
{
    try
    {
        if (panel == null) return;

        // Extract 100x100 section from main 200x200 display
        int bufferIndex = 0;

        for (int row = startRow; row < startRow + PANEL_SIZE; row++)
        {
            for (int col = startCol; col < startCol + PANEL_SIZE; col++)
            {
                if (row < ROWS_CT && col < COLUMNS_CT)
                    buffer[bufferIndex] = DRAW[row, col];
                else
                    buffer[bufferIndex] = B; // Background for out-of-bounds
                bufferIndex++;
            }
            // Add newline character
            buffer[bufferIndex] = '\n';
            bufferIndex++;
        }

        // Add random character at start for refresh (simplified)
        buffer[0] = (char)(RENDER_COUNTER % 10);

        // Convert to string and apply basic formatting
        string visualData = new string(buffer, 0, bufferIndex);
        // Add basic character replacement for proper display
        visualData = visualData.Replace(" ", "  ");

        // Write to panel
        panel.WriteText(visualData);
        panel.ContentType = ContentType.TEXT_AND_IMAGE;

        // Set font properties only once during initialization (moved to UIINIT)
        // panel.SetValue("FontSize", 0.173f);
        // panel.SetValue<long>("Font", 1147350002);
    }
    catch (Exception e)
    {
        Echo($"Error rendering {quadrant} quadrant: {e}");
    }
}
#endregion

#region Drawing Functions
/*=======================================================================================
  Function: Symbol Writer and Drawing Functions
  ---------------------------------------
  function will: Write symbols and draw chart elements for 200x200 display
//=======================================================================================*/
void DRAW_CHART()
{
    //Generating map Scale (adjusted for 200x200)
    int TENS = (int)Math.Floor(UI_SCALE / 200.0);
    int UNITS = (int)Math.Floor((UI_SCALE - TENS * 200) / 10.0);

    double SCALE = 120 - (UNITS * 2); // Scaled for larger display

    //Generates Unit Generation
    int TENSY = (int)Math.Floor(OFFSETY / SCALE);
    int UNITSY = (int)Math.Floor((OFFSETY - TENSY * SCALE));

    int TENSX = (int)Math.Floor(OFFSETX / SCALE);
    int UNITSX = (int)Math.Floor((OFFSETX - TENSX * SCALE));

    //Draws Grid Lines (scaled for 200x200) - Optimized with step size
    int stepSize = Math.Max(1, (int)SCALE / 4); // Reduce grid density for performance
    for (int i = 0; i < ROWS_CT; i += (int)SCALE)
    {
        for (int j = 0; j < COLUMNS_CT; j += stepSize)
        {
            if (i + UNITSY < ROWS_CT && j < COLUMNS_CT)
                BDRAW[i + UNITSY, j] = L2;
        }
    }

    for (int j = 0; j < COLUMNS_CT; j += (int)SCALE)
    {
        for (int i = 0; i < ROWS_CT; i += stepSize)
        {
            if (i < ROWS_CT && j + UNITSX < COLUMNS_CT)
                BDRAW[i, j + UNITSX] = L2;
        }
    }

    //Converts To Digital Format And Writes (scaled positions)
    int TENS_DISPLAY = (int)Math.Floor(UI_SCALE / 100.0);
    int UNITS_DISPLAY = (int)Math.Floor((UI_SCALE - TENS_DISPLAY * 100) / 10.0);

    WRT_SYMB(SYST_NBRS.NUMBERS[TENS_DISPLAY], SYST_NUMBERS_ROW, SYST_NUMBERS_COL);
    WRT_SYMB(SYST_NBRS.NUMBERS[UNITS_DISPLAY], SYST_NUMBERS_ROW, SYST_NUMBERS_COL + 16); // Scaled spacing

    //Mouse Scroll Converter (adjusted for 200x200)
    if ((POS_X < 5))
    { OFFSETX = OFFSETX + 2; CLICKPOS.X += 2; }
    if ((POS_X >= COLUMNS_CT - 5))
    { OFFSETX = OFFSETX - 2; CLICKPOS.X -= 2; }

    if ((POS_Y < 5))
    { OFFSETY = OFFSETY + 2; CLICKPOS.Y += 2; }
    if ((POS_Y >= COLUMNS_CT - 5))
    { OFFSETY = OFFSETY - 2; CLICKPOS.Y -= 2; }
}

public void WRT_SYMB(char[,] SYMBOL, int START_ROW, int START_COL)
{
    // Write enhanced symbol at given coordinates (bounds checking for massive display)
    for (int j = START_ROW; j < START_ROW + SYMBOL.GetLength(0); j++)
    {
        for (int i = START_COL; i < START_COL + SYMBOL.GetLength(1); i++)
        {
            if (j > ROWS_CT - 1 || i > COLUMNS_CT - 1 || i < 0 || j < 0) { continue; }
            if (SYMBOL[j - START_ROW, i - START_COL] == B || DRAW[j, i] == green) { continue; }
            DRAW[j, i] = SYMBOL[j - START_ROW, i - START_COL];
        }
    }
}

public void RASTER(ref Vector2D COORD)
{
    // Convert world coordinates to screen coordinates for massive display
    COORD = new Vector2D(MathHelper.Clamp(COORD.X, 0, COLUMNS_CT - 1), MathHelper.Clamp(COORD.Y, 0, ROWS_CT - 1));
}

// Placeholder drawing functions for objects
void WRT_DEI(DC_INF_INFO SQUAD)
{
    try
    {
        //Establishes Self Positions
        Vector3D MEPOS = Me.GetPosition();
        Vector3D MEPRIGHT = Me.WorldMatrix.Right;
        Vector3D MEPOSUP = Me.WorldMatrix.Up;
        Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

        //Loads Squadron Data
        Vector3D SHIPPOS = SQUAD.POSITION;
        char SYS_COLOUR = (SQUAD.TYPE != "EN") ? darkGray : red;

        //Calculates Relative Position
        Vector3D RELATIVE_POS = SHIPPOS - MEPOS;
        double DISTANCE = RELATIVE_POS.Length();

        // Convert to screen coordinates (scaled for 200x200)
        Vector2D SCREEN_POS = new Vector2D(
            OFFSETX + (RELATIVE_POS.Dot(MEPRIGHT) / UI_SCALE),
            OFFSETY + (RELATIVE_POS.Dot(MEPDOWN) / UI_SCALE)
        );

        RASTER(ref SCREEN_POS);

        // Draw object on screen
        if (SCREEN_POS.X >= 0 && SCREEN_POS.X < COLUMNS_CT && SCREEN_POS.Y >= 0 && SCREEN_POS.Y < ROWS_CT)
        {
            DRAW[(int)SCREEN_POS.Y, (int)SCREEN_POS.X] = SYS_COLOUR;
        }
    }
    catch (Exception e)
    {
        Echo($"Error drawing DEI: {e}");
    }
}

DRONE_INFO WRT_SQD(DRONE_INFO SQUAD)
{
    try
    {
        //Establishes Self Positions
        Vector3D MEPOS = Me.GetPosition();
        Vector3D MEPRIGHT = Me.WorldMatrix.Right;
        Vector3D MEPOSUP = Me.WorldMatrix.Up;
        Vector3D MEPDOWN = -Me.WorldMatrix.Forward;

        //Loads Squadron Data
        Vector3D SHIPPOS = SQUAD.LOC;

        //Calculates Relative Position
        Vector3D RELATIVE_POS = SHIPPOS - MEPOS;

        // Convert to screen coordinates (scaled for 200x200)
        Vector2D SCREEN_POS = new Vector2D(
            OFFSETX + (RELATIVE_POS.Dot(MEPRIGHT) / UI_SCALE),
            OFFSETY + (RELATIVE_POS.Dot(MEPDOWN) / UI_SCALE)
        );

        RASTER(ref SCREEN_POS);
        SQUAD.UIPOS = SCREEN_POS;

        // Draw squadron symbol
        if (SCREEN_POS.X >= 0 && SCREEN_POS.X < COLUMNS_CT && SCREEN_POS.Y >= 0 && SCREEN_POS.Y < ROWS_CT)
        {
            // Get appropriate symbol based on squadron type
            string SQUAD_TYPE = SQUAD.ID.Length >= 2 ? SQUAD.ID.Substring(0, 2) : "FR";
            if (SYST_SYMBLS.SYST_SYMBLS_PROCEDURAL.ContainsKey(SQUAD_TYPE))
            {
                WRT_SYMB(SYST_SYMBLS.SYST_SYMBLS_PROCEDURAL[SQUAD_TYPE], (int)SCREEN_POS.Y - 1, (int)SCREEN_POS.X - 3);
            }
            else
            {
                DRAW[(int)SCREEN_POS.Y, (int)SCREEN_POS.X] = P;
            }
        }
    }
    catch (Exception e)
    {
        Echo($"Error drawing squadron: {e}");
    }
    return SQUAD;
}
#endregion

#region Initialization Functions
/*=======================================================================================
  Function: System Initialization
  ---------------------------------------
  function will: Initialize all required blocks and systems for quad LCD operation
//=======================================================================================*/
void UIINIT()
{
    // Initialize required blocks for quad LCD system
    DISPLAY_PANEL_TL = GridTerminalSystem.GetBlockWithName("RFC_PANEL_TL") as IMyTextPanel;
    DISPLAY_PANEL_TR = GridTerminalSystem.GetBlockWithName("RFC_PANEL_TR") as IMyTextPanel;
    DISPLAY_PANEL_BL = GridTerminalSystem.GetBlockWithName("RFC_PANEL_BL") as IMyTextPanel;
    DISPLAY_PANEL_BR = GridTerminalSystem.GetBlockWithName("RFC_PANEL_BR") as IMyTextPanel;
    COMMAND_MODULE = GridTerminalSystem.GetBlockWithName("CENTRAL_COMMAND") as IMyProgrammableBlock;

    try
    {
        List<IMyTerminalBlock> TEMP_RC = new List<IMyTerminalBlock>();
        GridTerminalSystem.GetBlocksOfType<IMyShipController>(TEMP_RC, b => b.CubeGrid == Me.CubeGrid && b.CustomName == "RFC_RC");
        CONTROL = TEMP_RC[0] as IMyShipController;
    }
    catch
    {
        Echo("Warning: RFC_RC control block not found");
    }

    GridTerminalSystem.GetBlocksOfType<IMyGyro>(GYROS, b => b.CubeGrid == Me.CubeGrid);

    // Initialize background from custom data (scaled for 200x200)
    string BCKGRD_RAW = Me.CustomData;
    string BCKGRD = BCKGRD_RAW.Replace("\n", String.Empty);
    BCKGRD = BCKGRD.Replace(" ", String.Empty);

    // If custom data is properly sized for 200x200, use it; otherwise create default background
    if (BCKGRD.Length >= ROWS_CT * COLUMNS_CT)
    {
        for (int j = 0; j < ROWS_CT; j++)
        {
            for (int i = 0; i < COLUMNS_CT; i++)
            {
                char ITEM = (BCKGRD[(j * COLUMNS_CT) + i] == '0') ? L1 : B;
                BDRAW[j, i] = ITEM;
            }
        }
    }
    else
    {
        // Create default background for 200x200
        for (int j = 0; j < ROWS_CT; j++)
        {
            for (int i = 0; i < COLUMNS_CT; i++)
            {
                BDRAW[j, i] = B;
            }
        }
    }

    // Verify LCD panels are found and configure them
    if (DISPLAY_PANEL_TL == null)
        Echo("RFC_PANEL_TL not found");
    else
    {
        DISPLAY_PANEL_TL.SetValue("FontSize", 0.35f);
        DISPLAY_PANEL_TL.SetValue<long>("Font", 1147350002);
    }

    if (DISPLAY_PANEL_TR == null)
        Echo("RFC_PANEL_TR not found");
    else
    {
        DISPLAY_PANEL_TR.SetValue("FontSize", 0.35f);
        DISPLAY_PANEL_TR.SetValue<long>("Font", 1147350002);
    }

    if (DISPLAY_PANEL_BL == null)
        Echo("RFC_PANEL_BL not found");
    else
    {
        DISPLAY_PANEL_BL.SetValue("FontSize", 0.35f);
        DISPLAY_PANEL_BL.SetValue<long>("Font", 1147350002);
    }

    if (DISPLAY_PANEL_BR == null)
        Echo("RFC_PANEL_BR not found");
    else
    {
        DISPLAY_PANEL_BR.SetValue("FontSize", 0.35f);
        DISPLAY_PANEL_BR.SetValue<long>("Font", 1147350002);
    }
}

void SYSTINIT()
{
    // Initialize IGC communication
    IGCListener = IGC.RegisterBroadcastListener(IGCTagIN);

    // Initialize data structures
    DECENTIN_INFO.Clear();
    DRONES.Clear();
    DOCKPOINTS.Clear();

    // Set initial draw state
    DRAW = BDRAW.Clone() as char[,];

    Echo($"Fleet Command Quad LCD System Initialized - {VERSION}");
    Echo("Required LCD Panels: RFC_PANEL_TL, RFC_PANEL_TR, RFC_PANEL_BL, RFC_PANEL_BR");
    Echo("Required Control Block: RFC_RC");
}

// IGC Communication placeholder functions
void ProcessIGCMessages()
{
    try
    {
        while (IGCListener.HasPendingMessage)
        {
            var message = IGCListener.AcceptMessage();
            if (message.Tag == IGCTagIN)
            {
                string data = message.Data.ToString();
                // Process incoming fleet data
                ProcessFleetData(data);
            }
        }
    }
    catch (Exception e)
    {
        Echo($"IGC Error: {e}");
    }
}

void ProcessFleetData(string data)
{
    try
    {
        // Parse and process fleet communication data
        // This would contain drone updates, target information, etc.
        string[] parts = data.Split('|');

        foreach (string part in parts)
        {
            if (part.StartsWith("DRONE:"))
            {
                // Process drone data
                string droneData = part.Substring(6);
                // Parse drone information and update DRONES dictionary
            }
            else if (part.StartsWith("TARGET:"))
            {
                // Process target data
                string targetData = part.Substring(7);
                // Parse target information and update DECENTIN_INFO dictionary
            }
        }
    }
    catch (Exception e)
    {
        Echo($"Fleet Data Processing Error: {e}");
    }
}
#endregion
