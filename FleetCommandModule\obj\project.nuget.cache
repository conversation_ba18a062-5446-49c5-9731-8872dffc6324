{"version": 2, "dgSpecHash": "U/I8Ld3NN40=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\mal.mdk2.pbanalyzers\\2.1.13\\mal.mdk2.pbanalyzers.2.1.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mal.mdk2.pbpackager\\2.1.5\\mal.mdk2.pbpackager.2.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\mal.mdk2.references\\2.2.4\\mal.mdk2.references.2.2.4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.framework\\17.12.6\\microsoft.build.framework.17.12.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.build.utilities.core\\17.12.6\\microsoft.build.utilities.core.17.12.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.redist\\6.0.1\\microsoft.io.redist.6.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.stringtools\\17.12.6\\microsoft.net.stringtools.17.12.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.buffers\\4.5.1\\system.buffers.4.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.collections.immutable\\8.0.0\\system.collections.immutable.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.configuration.configurationmanager\\8.0.0\\system.configuration.configurationmanager.8.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.memory\\4.5.5\\system.memory.4.5.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"], "logs": []}