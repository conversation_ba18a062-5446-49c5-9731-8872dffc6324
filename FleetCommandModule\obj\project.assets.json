{"version": 3, "targets": {".NETFramework,Version=v4.8": {"Mal.Mdk2.PbAnalyzers/2.1.13": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"build/Mal.MDK2.PbAnalyzers.props": {}}}, "Mal.Mdk2.PbPackager/2.1.5": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"build/Mal.Mdk2.PbPackager.props": {}}}, "Mal.Mdk2.References/2.2.4": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "17.12.6", "Microsoft.Build.Utilities.Core": "17.12.6"}, "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}, "build": {"build/Mal.Mdk2.References.targets": {}}}, "Microsoft.Build.Framework/17.12.6": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "frameworkAssemblies": ["System.Xaml"], "compile": {"ref/net472/Microsoft.Build.Framework.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Build.Framework.dll": {"related": ".pdb;.xml"}}}, "Microsoft.Build.Utilities.Core/17.12.6": {"type": "package", "dependencies": {"Microsoft.Build.Framework": "17.12.6", "Microsoft.IO.Redist": "6.0.1", "Microsoft.NET.StringTools": "17.12.6", "System.Collections.Immutable": "8.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"ref/net472/Microsoft.Build.Utilities.Core.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.Build.Utilities.Core.dll": {"related": ".pdb;.xml"}}}, "Microsoft.IO.Redist/6.0.1": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.4"}, "compile": {"lib/net472/Microsoft.IO.Redist.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.IO.Redist.dll": {"related": ".xml"}}}, "Microsoft.NET.StringTools/17.12.6": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"ref/net472/Microsoft.NET.StringTools.dll": {"related": ".xml"}}, "runtime": {"lib/net472/Microsoft.NET.StringTools.dll": {"related": ".pdb;.xml"}}}, "System.Buffers/4.5.1": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"ref/net45/System.Buffers.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Buffers.dll": {"related": ".xml"}}}, "System.Collections.Immutable/8.0.0": {"type": "package", "dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "compile": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Collections.Immutable.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Configuration.ConfigurationManager/8.0.0": {"type": "package", "frameworkAssemblies": ["System.Configuration"], "compile": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "runtime": {"lib/net462/System.Configuration.ConfigurationManager.dll": {"related": ".xml"}}, "build": {"buildTransitive/net462/_._": {}}}, "System.Memory/4.5.5": {"type": "package", "dependencies": {"System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0", "System.Runtime.CompilerServices.Unsafe": "4.5.3"}, "frameworkAssemblies": ["System", "mscorlib"], "compile": {"lib/net461/System.Memory.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Memory.dll": {"related": ".xml"}}}, "System.Numerics.Vectors/4.5.0": {"type": "package", "frameworkAssemblies": ["System.Numerics", "mscorlib"], "compile": {"ref/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}, "runtime": {"lib/net46/System.Numerics.Vectors.dll": {"related": ".xml"}}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "frameworkAssemblies": ["mscorlib"], "compile": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}, "runtime": {"lib/net461/System.Runtime.CompilerServices.Unsafe.dll": {"related": ".xml"}}}}}, "libraries": {"Mal.Mdk2.PbAnalyzers/2.1.13": {"sha512": "yKjXW5qtiarh8S1BcAIXwJZnPA7c5CJqnukdOrhdSKFXVJeCSizbvUtA0TL2O9sBYD6drHnA4ni3xWmXQh9ENg==", "type": "package", "path": "mal.mdk2.pbanalyzers/2.1.13", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "analyzers/dotnet/cs/Mal.Mdk2.PbAnalyzers.dll", "analyzers/dotnet/cs/Microsoft.Extensions.FileSystemGlobbing.dll", "build/Mal.MDK2.PbAnalyzers.props", "lib/netstandard2.0/_._", "mal.mdk2.pbanalyzers.2.1.13.nupkg.sha512", "mal.mdk2.pbanalyzers.nuspec", "malware256.png", "readme.md", "tools/install.ps1", "tools/uninstall.ps1"]}, "Mal.Mdk2.PbPackager/2.1.5": {"sha512": "karSWOVkbCezYds+QsXr1XltyAnu0i88s4Mu+b+r+qBd4uh0kyYjiM8zZSpjMGCYIqW9wNM3fNM7SPWCscy+Ew==", "type": "package", "path": "mal.mdk2.pbpackager/2.1.5", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/Mal.Mdk2.PbPackager.props", "lib/netstandard2.0/_._", "mal.mdk2.pbpackager.2.1.5.nupkg.sha512", "mal.mdk2.pbpackager.nuspec", "malware256.png", "readme.md", "tools/Accessibility.dll", "tools/BuildHost-net472/Microsoft.Bcl.AsyncInterfaces.dll", "tools/BuildHost-net472/Microsoft.Build.Locator.dll", "tools/BuildHost-net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe", "tools/BuildHost-net472/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.exe.config", "tools/BuildHost-net472/Microsoft.IO.Redist.dll", "tools/BuildHost-net472/Newtonsoft.Json.dll", "tools/BuildHost-net472/System.Buffers.dll", "tools/BuildHost-net472/System.Collections.Immutable.dll", "tools/BuildHost-net472/System.CommandLine.dll", "tools/BuildHost-net472/System.Memory.dll", "tools/BuildHost-net472/System.Numerics.Vectors.dll", "tools/BuildHost-net472/System.Runtime.CompilerServices.Unsafe.dll", "tools/BuildHost-net472/System.Text.Encodings.Web.dll", "tools/BuildHost-net472/System.Text.Json.dll", "tools/BuildHost-net472/System.Threading.Tasks.Extensions.dll", "tools/BuildHost-net472/System.ValueTuple.dll", "tools/BuildHost-net472/cs/System.CommandLine.resources.dll", "tools/BuildHost-net472/de/System.CommandLine.resources.dll", "tools/BuildHost-net472/es/System.CommandLine.resources.dll", "tools/BuildHost-net472/fr/System.CommandLine.resources.dll", "tools/BuildHost-net472/it/System.CommandLine.resources.dll", "tools/BuildHost-net472/ja/System.CommandLine.resources.dll", "tools/BuildHost-net472/ko/System.CommandLine.resources.dll", "tools/BuildHost-net472/pl/System.CommandLine.resources.dll", "tools/BuildHost-net472/pt-BR/System.CommandLine.resources.dll", "tools/BuildHost-net472/ru/System.CommandLine.resources.dll", "tools/BuildHost-net472/tr/System.CommandLine.resources.dll", "tools/BuildHost-net472/zh-Hans/System.CommandLine.resources.dll", "tools/BuildHost-net472/zh-Hant/System.CommandLine.resources.dll", "tools/BuildHost-netcore/Microsoft.Build.Locator.dll", "tools/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.deps.json", "tools/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll", "tools/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll.config", "tools/BuildHost-netcore/Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.runtimeconfig.json", "tools/BuildHost-netcore/Newtonsoft.Json.dll", "tools/BuildHost-netcore/System.Collections.Immutable.dll", "tools/BuildHost-netcore/System.CommandLine.dll", "tools/BuildHost-netcore/System.Text.Encodings.Web.dll", "tools/BuildHost-netcore/System.Text.Json.dll", "tools/BuildHost-netcore/cs/System.CommandLine.resources.dll", "tools/BuildHost-netcore/de/System.CommandLine.resources.dll", "tools/BuildHost-netcore/es/System.CommandLine.resources.dll", "tools/BuildHost-netcore/fr/System.CommandLine.resources.dll", "tools/BuildHost-netcore/it/System.CommandLine.resources.dll", "tools/BuildHost-netcore/ja/System.CommandLine.resources.dll", "tools/BuildHost-netcore/ko/System.CommandLine.resources.dll", "tools/BuildHost-netcore/pl/System.CommandLine.resources.dll", "tools/BuildHost-netcore/pt-BR/System.CommandLine.resources.dll", "tools/BuildHost-netcore/ru/System.CommandLine.resources.dll", "tools/BuildHost-netcore/runtimes/browser/lib/net6.0/System.Text.Encodings.Web.dll", "tools/BuildHost-netcore/tr/System.CommandLine.resources.dll", "tools/BuildHost-netcore/zh-Hans/System.CommandLine.resources.dll", "tools/BuildHost-netcore/zh-Hant/System.CommandLine.resources.dll", "tools/D3DCompiler_47_cor3.dll", "tools/DirectWriteForwarder.dll", "tools/Humanizer.dll", "tools/ICSharpCode.AvalonEdit.dll", "tools/MdXaml.Plugins.dll", "tools/MdXaml.dll", "tools/Microsoft.Bcl.AsyncInterfaces.dll", "tools/Microsoft.Build.Framework.dll", "tools/Microsoft.Build.Locator.dll", "tools/Microsoft.Build.Tasks.Core.dll", "tools/Microsoft.Build.Utilities.Core.dll", "tools/Microsoft.Build.dll", "tools/Microsoft.CSharp.dll", "tools/Microsoft.CodeAnalysis.CSharp.Workspaces.dll", "tools/Microsoft.CodeAnalysis.CSharp.dll", "tools/Microsoft.CodeAnalysis.ExternalAccess.RazorCompiler.dll", "tools/Microsoft.CodeAnalysis.Workspaces.MSBuild.dll", "tools/Microsoft.CodeAnalysis.Workspaces.dll", "tools/Microsoft.CodeAnalysis.dll", "tools/Microsoft.DiaSymReader.Native.amd64.dll", "tools/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "tools/Microsoft.Extensions.DependencyInjection.dll", "tools/Microsoft.Extensions.FileSystemGlobbing.dll", "tools/Microsoft.Extensions.Logging.Abstractions.dll", "tools/Microsoft.Extensions.Logging.dll", "tools/Microsoft.Extensions.Options.dll", "tools/Microsoft.Extensions.Primitives.dll", "tools/Microsoft.NET.StringTools.dll", "tools/Microsoft.VisualBasic.Core.dll", "tools/Microsoft.VisualBasic.Forms.dll", "tools/Microsoft.VisualBasic.dll", "tools/Microsoft.VisualStudio.Setup.Configuration.Interop.dll", "tools/Microsoft.Win32.Primitives.dll", "tools/Microsoft.Win32.Registry.AccessControl.dll", "tools/Microsoft.Win32.Registry.dll", "tools/Microsoft.Win32.SystemEvents.dll", "tools/Newtonsoft.Json.dll", "tools/PenImc_cor3.dll", "tools/PresentationCore.dll", "tools/PresentationFramework-SystemCore.dll", "tools/PresentationFramework-SystemData.dll", "tools/PresentationFramework-SystemDrawing.dll", "tools/PresentationFramework-SystemXml.dll", "tools/PresentationFramework-SystemXmlLinq.dll", "tools/PresentationFramework.Aero.dll", "tools/PresentationFramework.Aero2.dll", "tools/PresentationFramework.AeroLite.dll", "tools/PresentationFramework.Classic.dll", "tools/PresentationFramework.Fluent.dll", "tools/PresentationFramework.Luna.dll", "tools/PresentationFramework.Royale.dll", "tools/PresentationFramework.dll", "tools/PresentationNative_cor3.dll", "tools/PresentationUI.dll", "tools/ReachFramework.dll", "tools/System.AppContext.dll", "tools/System.Buffers.dll", "tools/System.CodeDom.dll", "tools/System.Collections.Concurrent.dll", "tools/System.Collections.Immutable.dll", "tools/System.Collections.NonGeneric.dll", "tools/System.Collections.Specialized.dll", "tools/System.Collections.dll", "tools/System.ComponentModel.Annotations.dll", "tools/System.ComponentModel.DataAnnotations.dll", "tools/System.ComponentModel.EventBasedAsync.dll", "tools/System.ComponentModel.Primitives.dll", "tools/System.ComponentModel.TypeConverter.dll", "tools/System.ComponentModel.dll", "tools/System.Composition.AttributedModel.dll", "tools/System.Composition.Convention.dll", "tools/System.Composition.Hosting.dll", "tools/System.Composition.Runtime.dll", "tools/System.Composition.TypedParts.dll", "tools/System.Configuration.ConfigurationManager.dll", "tools/System.Configuration.dll", "tools/System.Console.dll", "tools/System.Core.dll", "tools/System.Data.Common.dll", "tools/System.Data.DataSetExtensions.dll", "tools/System.Data.dll", "tools/System.Design.dll", "tools/System.Diagnostics.Contracts.dll", "tools/System.Diagnostics.Debug.dll", "tools/System.Diagnostics.DiagnosticSource.dll", "tools/System.Diagnostics.EventLog.Messages.dll", "tools/System.Diagnostics.EventLog.dll", "tools/System.Diagnostics.FileVersionInfo.dll", "tools/System.Diagnostics.PerformanceCounter.dll", "tools/System.Diagnostics.Process.dll", "tools/System.Diagnostics.StackTrace.dll", "tools/System.Diagnostics.TextWriterTraceListener.dll", "tools/System.Diagnostics.Tools.dll", "tools/System.Diagnostics.TraceSource.dll", "tools/System.Diagnostics.Tracing.dll", "tools/System.DirectoryServices.dll", "tools/System.Drawing.Common.dll", "tools/System.Drawing.Design.dll", "tools/System.Drawing.Primitives.dll", "tools/System.Drawing.dll", "tools/System.Dynamic.Runtime.dll", "tools/System.Formats.Asn1.dll", "tools/System.Formats.Nrbf.dll", "tools/System.Formats.Tar.dll", "tools/System.Globalization.Calendars.dll", "tools/System.Globalization.Extensions.dll", "tools/System.Globalization.dll", "tools/System.IO.Compression.Brotli.dll", "tools/System.IO.Compression.FileSystem.dll", "tools/System.IO.Compression.Native.dll", "tools/System.IO.Compression.ZipFile.dll", "tools/System.IO.Compression.dll", "tools/System.IO.FileSystem.AccessControl.dll", "tools/System.IO.FileSystem.DriveInfo.dll", "tools/System.IO.FileSystem.Primitives.dll", "tools/System.IO.FileSystem.Watcher.dll", "tools/System.IO.FileSystem.dll", "tools/System.IO.Hashing.dll", "tools/System.IO.IsolatedStorage.dll", "tools/System.IO.MemoryMappedFiles.dll", "tools/System.IO.Packaging.dll", "tools/System.IO.Pipelines.dll", "tools/System.IO.Pipes.AccessControl.dll", "tools/System.IO.Pipes.dll", "tools/System.IO.UnmanagedMemoryStream.dll", "tools/System.IO.dll", "tools/System.Linq.Async.dll", "tools/System.Linq.Expressions.dll", "tools/System.Linq.Parallel.dll", "tools/System.Linq.Queryable.dll", "tools/System.Linq.dll", "tools/System.Memory.dll", "tools/System.Net.Http.Json.dll", "tools/System.Net.Http.dll", "tools/System.Net.HttpListener.dll", "tools/System.Net.Mail.dll", "tools/System.Net.NameResolution.dll", "tools/System.Net.NetworkInformation.dll", "tools/System.Net.Ping.dll", "tools/System.Net.Primitives.dll", "tools/System.Net.Quic.dll", "tools/System.Net.Requests.dll", "tools/System.Net.Security.dll", "tools/System.Net.ServicePoint.dll", "tools/System.Net.Sockets.dll", "tools/System.Net.WebClient.dll", "tools/System.Net.WebHeaderCollection.dll", "tools/System.Net.WebProxy.dll", "tools/System.Net.WebSockets.Client.dll", "tools/System.Net.WebSockets.dll", "tools/System.Net.dll", "tools/System.Numerics.Vectors.dll", "tools/System.Numerics.dll", "tools/System.ObjectModel.dll", "tools/System.Printing.dll", "tools/System.Private.CoreLib.dll", "tools/System.Private.DataContractSerialization.dll", "tools/System.Private.Uri.dll", "tools/System.Private.Windows.Core.dll", "tools/System.Private.Xml.Linq.dll", "tools/System.Private.Xml.dll", "tools/System.Reflection.DispatchProxy.dll", "tools/System.Reflection.Emit.ILGeneration.dll", "tools/System.Reflection.Emit.Lightweight.dll", "tools/System.Reflection.Emit.dll", "tools/System.Reflection.Extensions.dll", "tools/System.Reflection.Metadata.dll", "tools/System.Reflection.MetadataLoadContext.dll", "tools/System.Reflection.Primitives.dll", "tools/System.Reflection.TypeExtensions.dll", "tools/System.Reflection.dll", "tools/System.Resources.Extensions.dll", "tools/System.Resources.Reader.dll", "tools/System.Resources.ResourceManager.dll", "tools/System.Resources.Writer.dll", "tools/System.Runtime.CompilerServices.Unsafe.dll", "tools/System.Runtime.CompilerServices.VisualC.dll", "tools/System.Runtime.Extensions.dll", "tools/System.Runtime.Handles.dll", "tools/System.Runtime.InteropServices.JavaScript.dll", "tools/System.Runtime.InteropServices.RuntimeInformation.dll", "tools/System.Runtime.InteropServices.dll", "tools/System.Runtime.Intrinsics.dll", "tools/System.Runtime.Loader.dll", "tools/System.Runtime.Numerics.dll", "tools/System.Runtime.Serialization.Formatters.dll", "tools/System.Runtime.Serialization.Json.dll", "tools/System.Runtime.Serialization.Primitives.dll", "tools/System.Runtime.Serialization.Xml.dll", "tools/System.Runtime.Serialization.dll", "tools/System.Runtime.dll", "tools/System.Security.AccessControl.dll", "tools/System.Security.Claims.dll", "tools/System.Security.Cryptography.Algorithms.dll", "tools/System.Security.Cryptography.Cng.dll", "tools/System.Security.Cryptography.Csp.dll", "tools/System.Security.Cryptography.Encoding.dll", "tools/System.Security.Cryptography.OpenSsl.dll", "tools/System.Security.Cryptography.Pkcs.dll", "tools/System.Security.Cryptography.Primitives.dll", "tools/System.Security.Cryptography.ProtectedData.dll", "tools/System.Security.Cryptography.X509Certificates.dll", "tools/System.Security.Cryptography.Xml.dll", "tools/System.Security.Cryptography.dll", "tools/System.Security.Permissions.dll", "tools/System.Security.Principal.Windows.dll", "tools/System.Security.Principal.dll", "tools/System.Security.SecureString.dll", "tools/System.Security.dll", "tools/System.ServiceModel.Web.dll", "tools/System.ServiceProcess.dll", "tools/System.Text.Encoding.CodePages.dll", "tools/System.Text.Encoding.Extensions.dll", "tools/System.Text.Encoding.dll", "tools/System.Text.Encodings.Web.dll", "tools/System.Text.Json.dll", "tools/System.Text.RegularExpressions.dll", "tools/System.Threading.AccessControl.dll", "tools/System.Threading.Channels.dll", "tools/System.Threading.Overlapped.dll", "tools/System.Threading.Tasks.Dataflow.dll", "tools/System.Threading.Tasks.Extensions.dll", "tools/System.Threading.Tasks.Parallel.dll", "tools/System.Threading.Tasks.dll", "tools/System.Threading.Thread.dll", "tools/System.Threading.ThreadPool.dll", "tools/System.Threading.Timer.dll", "tools/System.Threading.dll", "tools/System.Transactions.Local.dll", "tools/System.Transactions.dll", "tools/System.ValueTuple.dll", "tools/System.Web.HttpUtility.dll", "tools/System.Web.dll", "tools/System.Windows.Controls.Ribbon.dll", "tools/System.Windows.Extensions.dll", "tools/System.Windows.Forms.Design.Editors.dll", "tools/System.Windows.Forms.Design.dll", "tools/System.Windows.Forms.Primitives.dll", "tools/System.Windows.Forms.dll", "tools/System.Windows.Input.Manipulations.dll", "tools/System.Windows.Presentation.dll", "tools/System.Windows.dll", "tools/System.Xaml.dll", "tools/System.Xml.Linq.dll", "tools/System.Xml.ReaderWriter.dll", "tools/System.Xml.Serialization.dll", "tools/System.Xml.XDocument.dll", "tools/System.Xml.XPath.XDocument.dll", "tools/System.Xml.XPath.dll", "tools/System.Xml.XmlDocument.dll", "tools/System.Xml.XmlSerializer.dll", "tools/System.Xml.dll", "tools/System.dll", "tools/UIAutomationClient.dll", "tools/UIAutomationClientSideProviders.dll", "tools/UIAutomationProvider.dll", "tools/UIAutomationTypes.dll", "tools/WindowsBase.dll", "tools/WindowsFormsIntegration.dll", "tools/checkdotnet.exe", "tools/clretwrc.dll", "tools/clrgc.dll", "tools/clrgcexp.dll", "tools/clrjit.dll", "tools/coreclr.dll", "tools/createdump.exe", "tools/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/cs/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/cs/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/cs/Microsoft.CodeAnalysis.resources.dll", "tools/cs/Microsoft.VisualBasic.Forms.resources.dll", "tools/cs/PresentationCore.resources.dll", "tools/cs/PresentationFramework.resources.dll", "tools/cs/PresentationUI.resources.dll", "tools/cs/ReachFramework.resources.dll", "tools/cs/System.Windows.Controls.Ribbon.resources.dll", "tools/cs/System.Windows.Forms.Design.resources.dll", "tools/cs/System.Windows.Forms.Primitives.resources.dll", "tools/cs/System.Windows.Forms.resources.dll", "tools/cs/System.Windows.Input.Manipulations.resources.dll", "tools/cs/System.Xaml.resources.dll", "tools/cs/UIAutomationClient.resources.dll", "tools/cs/UIAutomationClientSideProviders.resources.dll", "tools/cs/UIAutomationProvider.resources.dll", "tools/cs/UIAutomationTypes.resources.dll", "tools/cs/WindowsBase.resources.dll", "tools/cs/WindowsFormsIntegration.resources.dll", "tools/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/de/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/de/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/de/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/de/Microsoft.CodeAnalysis.resources.dll", "tools/de/Microsoft.VisualBasic.Forms.resources.dll", "tools/de/PresentationCore.resources.dll", "tools/de/PresentationFramework.resources.dll", "tools/de/PresentationUI.resources.dll", "tools/de/ReachFramework.resources.dll", "tools/de/System.Windows.Controls.Ribbon.resources.dll", "tools/de/System.Windows.Forms.Design.resources.dll", "tools/de/System.Windows.Forms.Primitives.resources.dll", "tools/de/System.Windows.Forms.resources.dll", "tools/de/System.Windows.Input.Manipulations.resources.dll", "tools/de/System.Xaml.resources.dll", "tools/de/UIAutomationClient.resources.dll", "tools/de/UIAutomationClientSideProviders.resources.dll", "tools/de/UIAutomationProvider.resources.dll", "tools/de/UIAutomationTypes.resources.dll", "tools/de/WindowsBase.resources.dll", "tools/de/WindowsFormsIntegration.resources.dll", "tools/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/es/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/es/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/es/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/es/Microsoft.CodeAnalysis.resources.dll", "tools/es/Microsoft.VisualBasic.Forms.resources.dll", "tools/es/PresentationCore.resources.dll", "tools/es/PresentationFramework.resources.dll", "tools/es/PresentationUI.resources.dll", "tools/es/ReachFramework.resources.dll", "tools/es/System.Windows.Controls.Ribbon.resources.dll", "tools/es/System.Windows.Forms.Design.resources.dll", "tools/es/System.Windows.Forms.Primitives.resources.dll", "tools/es/System.Windows.Forms.resources.dll", "tools/es/System.Windows.Input.Manipulations.resources.dll", "tools/es/System.Xaml.resources.dll", "tools/es/UIAutomationClient.resources.dll", "tools/es/UIAutomationClientSideProviders.resources.dll", "tools/es/UIAutomationProvider.resources.dll", "tools/es/UIAutomationTypes.resources.dll", "tools/es/WindowsBase.resources.dll", "tools/es/WindowsFormsIntegration.resources.dll", "tools/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/fr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/fr/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/fr/Microsoft.CodeAnalysis.resources.dll", "tools/fr/Microsoft.VisualBasic.Forms.resources.dll", "tools/fr/PresentationCore.resources.dll", "tools/fr/PresentationFramework.resources.dll", "tools/fr/PresentationUI.resources.dll", "tools/fr/ReachFramework.resources.dll", "tools/fr/System.Windows.Controls.Ribbon.resources.dll", "tools/fr/System.Windows.Forms.Design.resources.dll", "tools/fr/System.Windows.Forms.Primitives.resources.dll", "tools/fr/System.Windows.Forms.resources.dll", "tools/fr/System.Windows.Input.Manipulations.resources.dll", "tools/fr/System.Xaml.resources.dll", "tools/fr/UIAutomationClient.resources.dll", "tools/fr/UIAutomationClientSideProviders.resources.dll", "tools/fr/UIAutomationProvider.resources.dll", "tools/fr/UIAutomationTypes.resources.dll", "tools/fr/WindowsBase.resources.dll", "tools/fr/WindowsFormsIntegration.resources.dll", "tools/hostfxr.dll", "tools/hostpolicy.dll", "tools/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/it/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/it/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/it/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/it/Microsoft.CodeAnalysis.resources.dll", "tools/it/Microsoft.VisualBasic.Forms.resources.dll", "tools/it/PresentationCore.resources.dll", "tools/it/PresentationFramework.resources.dll", "tools/it/PresentationUI.resources.dll", "tools/it/ReachFramework.resources.dll", "tools/it/System.Windows.Controls.Ribbon.resources.dll", "tools/it/System.Windows.Forms.Design.resources.dll", "tools/it/System.Windows.Forms.Primitives.resources.dll", "tools/it/System.Windows.Forms.resources.dll", "tools/it/System.Windows.Input.Manipulations.resources.dll", "tools/it/System.Xaml.resources.dll", "tools/it/UIAutomationClient.resources.dll", "tools/it/UIAutomationClientSideProviders.resources.dll", "tools/it/UIAutomationProvider.resources.dll", "tools/it/UIAutomationTypes.resources.dll", "tools/it/WindowsBase.resources.dll", "tools/it/WindowsFormsIntegration.resources.dll", "tools/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/ja/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/ja/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/ja/Microsoft.CodeAnalysis.resources.dll", "tools/ja/Microsoft.VisualBasic.Forms.resources.dll", "tools/ja/PresentationCore.resources.dll", "tools/ja/PresentationFramework.resources.dll", "tools/ja/PresentationUI.resources.dll", "tools/ja/ReachFramework.resources.dll", "tools/ja/System.Windows.Controls.Ribbon.resources.dll", "tools/ja/System.Windows.Forms.Design.resources.dll", "tools/ja/System.Windows.Forms.Primitives.resources.dll", "tools/ja/System.Windows.Forms.resources.dll", "tools/ja/System.Windows.Input.Manipulations.resources.dll", "tools/ja/System.Xaml.resources.dll", "tools/ja/UIAutomationClient.resources.dll", "tools/ja/UIAutomationClientSideProviders.resources.dll", "tools/ja/UIAutomationProvider.resources.dll", "tools/ja/UIAutomationTypes.resources.dll", "tools/ja/WindowsBase.resources.dll", "tools/ja/WindowsFormsIntegration.resources.dll", "tools/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/ko/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/ko/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/ko/Microsoft.CodeAnalysis.resources.dll", "tools/ko/Microsoft.VisualBasic.Forms.resources.dll", "tools/ko/PresentationCore.resources.dll", "tools/ko/PresentationFramework.resources.dll", "tools/ko/PresentationUI.resources.dll", "tools/ko/ReachFramework.resources.dll", "tools/ko/System.Windows.Controls.Ribbon.resources.dll", "tools/ko/System.Windows.Forms.Design.resources.dll", "tools/ko/System.Windows.Forms.Primitives.resources.dll", "tools/ko/System.Windows.Forms.resources.dll", "tools/ko/System.Windows.Input.Manipulations.resources.dll", "tools/ko/System.Xaml.resources.dll", "tools/ko/UIAutomationClient.resources.dll", "tools/ko/UIAutomationClientSideProviders.resources.dll", "tools/ko/UIAutomationProvider.resources.dll", "tools/ko/UIAutomationTypes.resources.dll", "tools/ko/WindowsBase.resources.dll", "tools/ko/WindowsFormsIntegration.resources.dll", "tools/mdk.deps.json", "tools/mdk.dll", "tools/mdk.dll.config", "tools/mdk.exe", "tools/mdk.runtimeconfig.json", "tools/mdknotify-win.deps.json", "tools/mdknotify-win.dll", "tools/mdknotify-win.exe", "tools/mdknotify-win.runtimeconfig.json", "tools/mscordaccore.dll", "tools/mscordaccore_amd64_amd64_9.0.525.21509.dll", "tools/mscordbi.dll", "tools/mscorlib.dll", "tools/mscorrc.dll", "tools/msquic.dll", "tools/netstandard.dll", "tools/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/pl/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/pl/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/pl/Microsoft.CodeAnalysis.resources.dll", "tools/pl/Microsoft.VisualBasic.Forms.resources.dll", "tools/pl/PresentationCore.resources.dll", "tools/pl/PresentationFramework.resources.dll", "tools/pl/PresentationUI.resources.dll", "tools/pl/ReachFramework.resources.dll", "tools/pl/System.Windows.Controls.Ribbon.resources.dll", "tools/pl/System.Windows.Forms.Design.resources.dll", "tools/pl/System.Windows.Forms.Primitives.resources.dll", "tools/pl/System.Windows.Forms.resources.dll", "tools/pl/System.Windows.Input.Manipulations.resources.dll", "tools/pl/System.Xaml.resources.dll", "tools/pl/UIAutomationClient.resources.dll", "tools/pl/UIAutomationClientSideProviders.resources.dll", "tools/pl/UIAutomationProvider.resources.dll", "tools/pl/UIAutomationTypes.resources.dll", "tools/pl/WindowsBase.resources.dll", "tools/pl/WindowsFormsIntegration.resources.dll", "tools/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/pt-BR/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/pt-BR/Microsoft.CodeAnalysis.resources.dll", "tools/pt-BR/Microsoft.VisualBasic.Forms.resources.dll", "tools/pt-BR/PresentationCore.resources.dll", "tools/pt-BR/PresentationFramework.resources.dll", "tools/pt-BR/PresentationUI.resources.dll", "tools/pt-BR/ReachFramework.resources.dll", "tools/pt-BR/System.Windows.Controls.Ribbon.resources.dll", "tools/pt-BR/System.Windows.Forms.Design.resources.dll", "tools/pt-BR/System.Windows.Forms.Primitives.resources.dll", "tools/pt-BR/System.Windows.Forms.resources.dll", "tools/pt-BR/System.Windows.Input.Manipulations.resources.dll", "tools/pt-BR/System.Xaml.resources.dll", "tools/pt-BR/UIAutomationClient.resources.dll", "tools/pt-BR/UIAutomationClientSideProviders.resources.dll", "tools/pt-BR/UIAutomationProvider.resources.dll", "tools/pt-BR/UIAutomationTypes.resources.dll", "tools/pt-BR/WindowsBase.resources.dll", "tools/pt-BR/WindowsFormsIntegration.resources.dll", "tools/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/ru/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/ru/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/ru/Microsoft.CodeAnalysis.resources.dll", "tools/ru/Microsoft.VisualBasic.Forms.resources.dll", "tools/ru/PresentationCore.resources.dll", "tools/ru/PresentationFramework.resources.dll", "tools/ru/PresentationUI.resources.dll", "tools/ru/ReachFramework.resources.dll", "tools/ru/System.Windows.Controls.Ribbon.resources.dll", "tools/ru/System.Windows.Forms.Design.resources.dll", "tools/ru/System.Windows.Forms.Primitives.resources.dll", "tools/ru/System.Windows.Forms.resources.dll", "tools/ru/System.Windows.Input.Manipulations.resources.dll", "tools/ru/System.Xaml.resources.dll", "tools/ru/UIAutomationClient.resources.dll", "tools/ru/UIAutomationClientSideProviders.resources.dll", "tools/ru/UIAutomationProvider.resources.dll", "tools/ru/UIAutomationTypes.resources.dll", "tools/ru/WindowsBase.resources.dll", "tools/ru/WindowsFormsIntegration.resources.dll", "tools/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/tr/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/tr/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/tr/Microsoft.CodeAnalysis.resources.dll", "tools/tr/Microsoft.VisualBasic.Forms.resources.dll", "tools/tr/PresentationCore.resources.dll", "tools/tr/PresentationFramework.resources.dll", "tools/tr/PresentationUI.resources.dll", "tools/tr/ReachFramework.resources.dll", "tools/tr/System.Windows.Controls.Ribbon.resources.dll", "tools/tr/System.Windows.Forms.Design.resources.dll", "tools/tr/System.Windows.Forms.Primitives.resources.dll", "tools/tr/System.Windows.Forms.resources.dll", "tools/tr/System.Windows.Input.Manipulations.resources.dll", "tools/tr/System.Xaml.resources.dll", "tools/tr/UIAutomationClient.resources.dll", "tools/tr/UIAutomationClientSideProviders.resources.dll", "tools/tr/UIAutomationProvider.resources.dll", "tools/tr/UIAutomationTypes.resources.dll", "tools/tr/WindowsBase.resources.dll", "tools/tr/WindowsFormsIntegration.resources.dll", "tools/vcruntime140_cor3.dll", "tools/wpfgfx_cor3.dll", "tools/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/zh-Hans/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/zh-Hans/Microsoft.CodeAnalysis.resources.dll", "tools/zh-Hans/Microsoft.VisualBasic.Forms.resources.dll", "tools/zh-Hans/PresentationCore.resources.dll", "tools/zh-Hans/PresentationFramework.resources.dll", "tools/zh-Hans/PresentationUI.resources.dll", "tools/zh-Hans/ReachFramework.resources.dll", "tools/zh-Hans/System.Windows.Controls.Ribbon.resources.dll", "tools/zh-Hans/System.Windows.Forms.Design.resources.dll", "tools/zh-Hans/System.Windows.Forms.Primitives.resources.dll", "tools/zh-Hans/System.Windows.Forms.resources.dll", "tools/zh-Hans/System.Windows.Input.Manipulations.resources.dll", "tools/zh-Hans/System.Xaml.resources.dll", "tools/zh-Hans/UIAutomationClient.resources.dll", "tools/zh-Hans/UIAutomationClientSideProviders.resources.dll", "tools/zh-Hans/UIAutomationProvider.resources.dll", "tools/zh-Hans/UIAutomationTypes.resources.dll", "tools/zh-Hans/WindowsBase.resources.dll", "tools/zh-Hans/WindowsFormsIntegration.resources.dll", "tools/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll", "tools/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll", "tools/zh-Hant/Microsoft.CodeAnalysis.Workspaces.MSBuild.resources.dll", "tools/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll", "tools/zh-Hant/Microsoft.CodeAnalysis.resources.dll", "tools/zh-Hant/Microsoft.VisualBasic.Forms.resources.dll", "tools/zh-Hant/PresentationCore.resources.dll", "tools/zh-Hant/PresentationFramework.resources.dll", "tools/zh-Hant/PresentationUI.resources.dll", "tools/zh-Hant/ReachFramework.resources.dll", "tools/zh-Hant/System.Windows.Controls.Ribbon.resources.dll", "tools/zh-Hant/System.Windows.Forms.Design.resources.dll", "tools/zh-Hant/System.Windows.Forms.Primitives.resources.dll", "tools/zh-Hant/System.Windows.Forms.resources.dll", "tools/zh-Hant/System.Windows.Input.Manipulations.resources.dll", "tools/zh-Hant/System.Xaml.resources.dll", "tools/zh-Hant/UIAutomationClient.resources.dll", "tools/zh-Hant/UIAutomationClientSideProviders.resources.dll", "tools/zh-Hant/UIAutomationProvider.resources.dll", "tools/zh-Hant/UIAutomationTypes.resources.dll", "tools/zh-Hant/WindowsBase.resources.dll", "tools/zh-Hant/WindowsFormsIntegration.resources.dll"]}, "Mal.Mdk2.References/2.2.4": {"sha512": "tp4apqR1DA+RdCTIkMYUetbg5ONebLJZW/xv9EABVHn9GLR+mtcW3e0fe61DqW7y0UY1HSkJcxDBTOLdCoQikA==", "type": "package", "path": "mal.mdk2.references/2.2.4", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/Mal.Mdk2.References.targets", "lib/netstandard2.0/_._", "mal.mdk2.references.2.2.4.nupkg.sha512", "mal.mdk2.references.nuspec", "malware256.png", "readme.md", "tools/netstandard2.0/cs/Mal.Mdk2.References.dll"]}, "Microsoft.Build.Framework/17.12.6": {"sha512": "jleteC0seumLGTmTVwob97lcwPj/dfgzL/V3g/VVcMZgo2Ic7jzdy8AYpByPDh8e3uRq0SjCl6HOFCjhy5GzRQ==", "type": "package", "path": "microsoft.build.framework/17.12.6", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Framework.dll", "lib/net472/Microsoft.Build.Framework.pdb", "lib/net472/Microsoft.Build.Framework.xml", "lib/net9.0/Microsoft.Build.Framework.dll", "lib/net9.0/Microsoft.Build.Framework.pdb", "lib/net9.0/Microsoft.Build.Framework.xml", "microsoft.build.framework.17.12.6.nupkg.sha512", "microsoft.build.framework.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Framework.dll", "ref/net472/Microsoft.Build.Framework.xml", "ref/net9.0/Microsoft.Build.Framework.dll", "ref/net9.0/Microsoft.Build.Framework.xml", "ref/netstandard2.0/Microsoft.Build.Framework.dll", "ref/netstandard2.0/Microsoft.Build.Framework.xml"]}, "Microsoft.Build.Utilities.Core/17.12.6": {"sha512": "pU3GnHcXp8VRMGKxdJCq+tixfhFn+QwEbpqmZmc/nqFHFyuhlGwjonWZMIWcwuCv/8EHgxoOttFvna1vrN+RrA==", "type": "package", "path": "microsoft.build.utilities.core/17.12.6", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.Build.Utilities.Core.dll", "lib/net472/Microsoft.Build.Utilities.Core.pdb", "lib/net472/Microsoft.Build.Utilities.Core.xml", "lib/net9.0/Microsoft.Build.Utilities.Core.dll", "lib/net9.0/Microsoft.Build.Utilities.Core.pdb", "lib/net9.0/Microsoft.Build.Utilities.Core.xml", "microsoft.build.utilities.core.17.12.6.nupkg.sha512", "microsoft.build.utilities.core.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.Build.Utilities.Core.dll", "ref/net472/Microsoft.Build.Utilities.Core.xml", "ref/net9.0/Microsoft.Build.Utilities.Core.dll", "ref/net9.0/Microsoft.Build.Utilities.Core.xml", "ref/netstandard2.0/Microsoft.Build.Utilities.Core.dll", "ref/netstandard2.0/Microsoft.Build.Utilities.Core.xml"]}, "Microsoft.IO.Redist/6.0.1": {"sha512": "bSapbVhuyHtTHXRJwLwhzkOIUPXXMxxJ9a1/gjXb7gtk9949aggm17v+Hb1biv6CTl1DWoZ6YcxVFrGQRGeETQ==", "type": "package", "path": "microsoft.io.redist/6.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net472/Microsoft.IO.Redist.dll", "lib/net472/Microsoft.IO.Redist.xml", "microsoft.io.redist.6.0.1.nupkg.sha512", "microsoft.io.redist.nuspec", "useSharedDesignerContext.txt"]}, "Microsoft.NET.StringTools/17.12.6": {"sha512": "w8Ehofqte5bJoR+Fa3f6JwkwFEkGtXxqvQHGOVOSHDzgNVySvL5FSNhavbQSZ864el9c3rjdLPLAtBW8dq6fmg==", "type": "package", "path": "microsoft.net.stringtools/17.12.6", "files": [".nupkg.metadata", ".signature.p7s", "MSBuild-NuGet-Icon.png", "README.md", "lib/net472/Microsoft.NET.StringTools.dll", "lib/net472/Microsoft.NET.StringTools.pdb", "lib/net472/Microsoft.NET.StringTools.xml", "lib/net9.0/Microsoft.NET.StringTools.dll", "lib/net9.0/Microsoft.NET.StringTools.pdb", "lib/net9.0/Microsoft.NET.StringTools.xml", "lib/netstandard2.0/Microsoft.NET.StringTools.dll", "lib/netstandard2.0/Microsoft.NET.StringTools.pdb", "lib/netstandard2.0/Microsoft.NET.StringTools.xml", "microsoft.net.stringtools.17.12.6.nupkg.sha512", "microsoft.net.stringtools.nuspec", "notices/THIRDPARTYNOTICES.txt", "ref/net472/Microsoft.NET.StringTools.dll", "ref/net472/Microsoft.NET.StringTools.xml", "ref/net9.0/Microsoft.NET.StringTools.dll", "ref/net9.0/Microsoft.NET.StringTools.xml", "ref/netstandard2.0/Microsoft.NET.StringTools.dll", "ref/netstandard2.0/Microsoft.NET.StringTools.xml"]}, "System.Buffers/4.5.1": {"sha512": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "type": "package", "path": "system.buffers/4.5.1", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Buffers.dll", "lib/net461/System.Buffers.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.1/System.Buffers.dll", "lib/netstandard1.1/System.Buffers.xml", "lib/netstandard2.0/System.Buffers.dll", "lib/netstandard2.0/System.Buffers.xml", "lib/uap10.0.16299/_._", "ref/net45/System.Buffers.dll", "ref/net45/System.Buffers.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.1/System.Buffers.dll", "ref/netstandard1.1/System.Buffers.xml", "ref/netstandard2.0/System.Buffers.dll", "ref/netstandard2.0/System.Buffers.xml", "ref/uap10.0.16299/_._", "system.buffers.4.5.1.nupkg.sha512", "system.buffers.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Collections.Immutable/8.0.0": {"sha512": "AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "type": "package", "path": "system.collections.immutable/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Collections.Immutable.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Collections.Immutable.targets", "lib/net462/System.Collections.Immutable.dll", "lib/net462/System.Collections.Immutable.xml", "lib/net6.0/System.Collections.Immutable.dll", "lib/net6.0/System.Collections.Immutable.xml", "lib/net7.0/System.Collections.Immutable.dll", "lib/net7.0/System.Collections.Immutable.xml", "lib/net8.0/System.Collections.Immutable.dll", "lib/net8.0/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "system.collections.immutable.8.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt"]}, "System.Configuration.ConfigurationManager/8.0.0": {"sha512": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "type": "package", "path": "system.configuration.configurationmanager/8.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "PACKAGE.md", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/net461/System.Configuration.ConfigurationManager.targets", "buildTransitive/net462/_._", "buildTransitive/net6.0/_._", "buildTransitive/netcoreapp2.0/System.Configuration.ConfigurationManager.targets", "lib/net462/System.Configuration.ConfigurationManager.dll", "lib/net462/System.Configuration.ConfigurationManager.xml", "lib/net6.0/System.Configuration.ConfigurationManager.dll", "lib/net6.0/System.Configuration.ConfigurationManager.xml", "lib/net7.0/System.Configuration.ConfigurationManager.dll", "lib/net7.0/System.Configuration.ConfigurationManager.xml", "lib/net8.0/System.Configuration.ConfigurationManager.dll", "lib/net8.0/System.Configuration.ConfigurationManager.xml", "lib/netstandard2.0/System.Configuration.ConfigurationManager.dll", "lib/netstandard2.0/System.Configuration.ConfigurationManager.xml", "system.configuration.configurationmanager.8.0.0.nupkg.sha512", "system.configuration.configurationmanager.nuspec", "useSharedDesignerContext.txt"]}, "System.Memory/4.5.5": {"sha512": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "type": "package", "path": "system.memory/4.5.5", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Memory.dll", "lib/net461/System.Memory.xml", "lib/netcoreapp2.1/_._", "lib/netstandard1.1/System.Memory.dll", "lib/netstandard1.1/System.Memory.xml", "lib/netstandard2.0/System.Memory.dll", "lib/netstandard2.0/System.Memory.xml", "ref/netcoreapp2.1/_._", "system.memory.4.5.5.nupkg.sha512", "system.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Numerics.Vectors/4.5.0": {"sha512": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "type": "package", "path": "system.numerics.vectors/4.5.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net46/System.Numerics.Vectors.dll", "lib/net46/System.Numerics.Vectors.xml", "lib/netcoreapp2.0/_._", "lib/netstandard1.0/System.Numerics.Vectors.dll", "lib/netstandard1.0/System.Numerics.Vectors.xml", "lib/netstandard2.0/System.Numerics.Vectors.dll", "lib/netstandard2.0/System.Numerics.Vectors.xml", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.dll", "lib/portable-net45+win8+wp8+wpa81/System.Numerics.Vectors.xml", "lib/uap10.0.16299/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/System.Numerics.Vectors.dll", "ref/net45/System.Numerics.Vectors.xml", "ref/net46/System.Numerics.Vectors.dll", "ref/net46/System.Numerics.Vectors.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/System.Numerics.Vectors.dll", "ref/netstandard1.0/System.Numerics.Vectors.xml", "ref/netstandard2.0/System.Numerics.Vectors.dll", "ref/netstandard2.0/System.Numerics.Vectors.xml", "ref/uap10.0.16299/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.numerics.vectors.4.5.0.nupkg.sha512", "system.numerics.vectors.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"sha512": "/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "type": "package", "path": "system.runtime.compilerservices.unsafe/6.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "buildTransitive/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.targets", "buildTransitive/netcoreapp3.1/_._", "lib/net461/System.Runtime.CompilerServices.Unsafe.dll", "lib/net461/System.Runtime.CompilerServices.Unsafe.xml", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/net6.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt"]}}, "projectFileDependencyGroups": {".NETFramework,Version=v4.8": ["Mal.Mdk2.PbAnalyzers >= 2.1.13", "Mal.Mdk2.PbPackager >= 2.1.5", "Mal.Mdk2.References >= 2.2.4"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj", "projectName": "MyFirstScript", "projectPath": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\MyFirstScript.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\FleetCommand\\FleetCommandModule\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netframework48"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net48": {"targetAlias": "netframework48", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net48": {"targetAlias": "netframework48", "dependencies": {"Mal.Mdk2.PbAnalyzers": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.1.13, )"}, "Mal.Mdk2.PbPackager": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[2.1.5, )"}, "Mal.Mdk2.References": {"target": "Package", "version": "[2.2.4, )"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.305\\RuntimeIdentifierGraph.json"}}}}